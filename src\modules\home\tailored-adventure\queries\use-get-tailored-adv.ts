import { ITailoredAdventure } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function UseGetTailoredAdventure() {
  return useQuery<IApiResponse<ITailoredAdventure>, Error>({
    queryKey: ['tailored-adventure'],
    queryFn: () =>
      fetch(`/api/home-tailored-adventures`, {
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((res) => res.json()),
  });
}
