'use client';

import { useEffect, useState } from 'react';
import { CollapsibleSection } from '../component/collapsible';
import { PackageDetailsForm } from '../component/package-detail-form';
import { SeoDetailsSection } from '@/components/package/seo-detail-section';
import { SchemaDetailsSection } from '@/components/package/seo-schema-detail-section';
import { PackageFormData } from '@/types/package';
import { Button } from '@/components/ui/button';

interface PackageEditPageProps {
  packageId: string;
  initialData?: PackageFormData;
  onSave?: (data: PackageFormData) => void;
  onCancel?: () => void;
  fetchPackageData?: (id: string) => Promise<PackageFormData>;
}

export default function PackageEditPage({
  packageId,
  initialData,
  onSave,
  onCancel,
  fetchPackageData,
}: PackageEditPageProps) {
  const [packageData, setPackageData] = useState<PackageFormData | null>(
    initialData || null
  );
  const [loading, setLoading] = useState<boolean>(!initialData);
  const [seoOpen, setSeoOpen] = useState(true);
  const [schemaOpen, setSchemaOpen] = useState(false);
  const [packageEditOpen, setPackageEditOpen] = useState(true);

  useEffect(() => {
    if (!initialData && fetchPackageData) {
      setLoading(true);
      fetchPackageData(packageId).then((data) => {
        setPackageData(data);
        setLoading(false);
      });
    }
  }, [initialData, fetchPackageData, packageId]);

  const handleSave = () => {
    if (!packageData) return;
    if (onSave) {
      onSave(packageData);
    } else {
      console.log('Updating package data:', packageData);
      // TODO: Implement actual update logic here (API call)
    }
  };

  if (loading || !packageData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p className="text-gray-600">Loading package data...</p>
      </div>
    );
  }

  return <div className="min-h-screen bg-gray-50 p-6"></div>;
}
