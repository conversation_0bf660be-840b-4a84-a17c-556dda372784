'use client';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import dynamic from 'next/dynamic';
import { IPackageInclusions, IPackageExclusions } from '@/types/package_';

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), {
  ssr: false,
});

interface PackageInclusionsExclusionsFormProps {
  inclusions?: Partial<IPackageInclusions> | undefined;
  exclusions?: Partial<IPackageExclusions> | undefined;
  onInclusionsChange: (inclusions: Partial<IPackageInclusions>) => void;
  onExclusionsChange: (exclusions: Partial<IPackageExclusions>) => void;
}

export function PackageInclusionsExclusionsForm({
  inclusions,
  exclusions,
  onInclusionsChange,
  onExclusionsChange,
}: PackageInclusionsExclusionsFormProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Inclusions */}
      <Card>
        <CardHeader>
          <CardTitle>What&apos;s Included</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="inclusions-title">Title</Label>
            <Input
              id="inclusions-title"
              value={inclusions?.title || ''}
              onChange={(e) =>
                onInclusionsChange({ ...inclusions, title: e.target.value })
              }
              placeholder="What's Included"
              className="border-black/20 rounded-none"
            />
          </div>
          <div>
            <Label htmlFor="inclusions-details">Details</Label>
            <div className="mt-1">
              <RichTextEditor
                value={inclusions?.details || ''}
                onChange={(data) =>
                  onInclusionsChange({ ...inclusions, details: data })
                }
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Exclusions */}
      <Card>
        <CardHeader>
          <CardTitle>What&apos;s Not Included</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="exclusions-title">Title</Label>
            <Input
              id="exclusions-title"
              value={exclusions?.title || ''}
              onChange={(e) =>
                onExclusionsChange({ ...exclusions, title: e.target.value })
              }
              placeholder="What's Not Included"
              className="border-black/20 rounded-none"
            />
          </div>
          <div>
            <Label htmlFor="exclusions-details">Details</Label>
            <div className="mt-1">
              <RichTextEditor
                value={exclusions?.details || ''}
                onChange={(data) =>
                  onExclusionsChange({ ...exclusions, details: data })
                }
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
