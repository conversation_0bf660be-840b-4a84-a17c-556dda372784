'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { EditTabs } from '@/modules/product/component/edit-tabs'
import { useCreatePackageReview } from '@/modules/package-review/mutations/create-package-review'
import { useUpdatePackageReview } from '@/modules/package-review/mutations/update-package-review'
import { useDeletePackageReview } from '@/modules/package-review/mutations/delete-package-review'
import { IPackageReview } from '@/types/package_'
import { toast } from 'sonner'
import { AddReviewForm } from '@/modules/product/component/add-review-form'
import { ReviewList } from '@/modules/product/component/list-review'
import { useGetPackageReviewById } from '@/modules/package-review/queries/get-package-review-by-packageId'

export default function EditReviewPage() {
  const { id: packageId } = useParams() as { id: string }

  const { data, isLoading, isError } = useGetPackageReviewById(packageId);

  const createMutation = useCreatePackageReview()
  const updateMutation = useUpdatePackageReview(packageId)
  const deleteMutation = useDeletePackageReview(packageId)

  const [items, setItems] = useState<IPackageReview[]>([])
  const [editingItem, setEditingItem] = useState<IPackageReview | null>(null)

  useEffect(() => {
    if (data?.data) {
      const reviews: IPackageReview[] = Array.isArray(data.data)
        ? data.data.filter(Boolean)
        : [data.data]
      setItems(reviews)
    }
  }, [data])

  const onAdd = (newItem: Omit<IPackageReview, 'id' | 'createdAt' | 'updatedAt'>) => {
    createMutation.mutate(
      { ...newItem, packageId },
      {
        onSuccess: res => {
          if (res.data) {
            setItems(prev => [...prev, res.data!])
            toast.success('Review added successfully')
          }
        },
        onError: (error) => {
          console.error('Create failed:', error)
          toast.error('Failed to add review')
        },
      }
    )
  }

  const onUpdate = (updatedItem: IPackageReview) => {
    updateMutation.mutate(
      { ...updatedItem, packageId },
      {
        onSuccess: () => {
          setEditingItem(null)
          toast.success('Review updated successfully')
        },
        onError: (error) => {
          console.error('Update failed:', error)
          toast.error('Failed to update review')
        },
      }
    )
  }

  const onDelete = (id: string) => {
    deleteMutation.mutate(id, {
      onSuccess: () => toast.success('Review deleted successfully'),
      onError: () => toast.error('Failed to delete review'),
    })
  }

  const onEdit = (id: string) => {
    const foundItem = items.find(i => String(i.id) === String(id))
    setEditingItem(foundItem || null)
  }

  if (isLoading) return <div>Loading reviews...</div>
  if (isError) return <div>Error loading review data.</div>

  return (
    <div className="min-h-screen p-6 bg-gray-50">
      <EditTabs packageId={packageId} />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <AddReviewForm
          editingItem={editingItem}
          onAdd={onAdd}
          onUpdate={onUpdate}
          onCancelEdit={() => setEditingItem(null)}
        />
        <ReviewList
          items={items}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      </div>
    </div>
  )
}