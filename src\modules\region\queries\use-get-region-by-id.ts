import { IRegion } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetRegionById(id: string) {
  return useQuery<IApiResponse<IRegion>, Error>({
    queryKey: ['region', id],
    queryFn: () =>
      fetch(`/api/region/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id, 
  });
}
