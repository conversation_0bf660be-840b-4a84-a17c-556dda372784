"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { UseGetHikingArea } from "../hiking-areas/queries/use-get-hiking-area";
import Link from "next/link";
import { Pen, Trash } from "lucide-react";
import { UseUpdateHikingArea } from "../hiking-areas/mutations/use-update-hiking-area";
import { toast } from "sonner";

const HikingAreaSection: React.FC = () => {
    const { data, isLoading, error } = UseGetHikingArea();
    const updateHikingArea = UseUpdateHikingArea();

    const [hikingAreaBlock, setHikingAreaBlock] = useState(data?.data || null);

    useEffect(() => {
        if (data?.data) {
            setHikingAreaBlock(data.data);
        }
    }, [data]);

    const handleDelete = (idToDelete: string) => {
        if (!hikingAreaBlock) return;

        const confirmed = window.confirm("Are you sure you want to delete this item?");
        if (!confirmed) return;

        const updatedAreas = hikingAreaBlock.areas.filter(area => area.id !== idToDelete);
        const updatedHikingArea = { ...hikingAreaBlock, areas: updatedAreas };

        updateHikingArea.mutate(updatedHikingArea, {
            onSuccess: () => {
                toast.success("Item deleted successfully.");
                setHikingAreaBlock(updatedHikingArea);
            },
            onError: (err) => {
                toast.error(`Delete failed: ${err.message}`);
            },
        });
    };


    const router = useRouter();

    if (isLoading) return <p>Loading hiking areas...</p>;
    if (error) return <p>Error loading hiking areas: {error.message}</p>;


    if (!hikingAreaBlock) return <p>No hiking area data found.</p>;

    // const hikingAreaBlock = data?.data;

    return (
        <div>
            <section className="p-6">
                <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
                    {hikingAreaBlock ? (
                        <div>
                            <div className="flex items-center justify-between mb-6">
                                <h1 className="text-3xl font-bold">Heading: {hikingAreaBlock.heading}</h1>
                                <button
                                    className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition"
                                    onClick={() => router.push("/home/<USER>/edit")}
                                >
                                    Edit Hiking Areas
                                </button>
                            </div>

                            {hikingAreaBlock.subHeading && (
                                <p className="mb-6">Sub Heading: {hikingAreaBlock.subHeading}</p>
                            )}

                            <div className="bg-white rounded-lg shadow">
                                {hikingAreaBlock.areas.length > 0 ? (
                                    <table className="min-w-full text-left border-separate border-spacing-0">
                                        <thead>
                                            <tr className="bg-gray-100">
                                                <th className="border px-4 py-2">SN</th>
                                                <th className="border px-4 py-2">Image</th>
                                                <th className="border px-4 py-2">Title</th>
                                                <th className="border px-4 py-2">Subtitle</th>
                                                <th className="border px-4 py-2">Link</th>
                                                <th className="border px-4 py-2">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {hikingAreaBlock.areas.map((area, idx) => (
                                                <tr key={area.id} className="even:bg-gray-50">
                                                    <td className="border px-4 py-2">{idx + 1}</td>
                                                    <td className="border px-4 py-2">
                                                        {area.image && !area.image.includes("example.com") ? (
                                                            <Image
                                                                src={area.image}
                                                                alt={area.title}
                                                                width={100}
                                                                height={60}
                                                                className="rounded object-cover"
                                                            />
                                                        ) : (
                                                            <Image
                                                                src="/images/placeholder.jpg"
                                                                alt="placeholder"
                                                                width={100}
                                                                height={60}
                                                            />
                                                        )}
                                                    </td>
                                                    <td className="border px-4 py-2">{area.title}</td>
                                                    <td className="border px-4 py-2">{area.subtitle}</td>
                                                    <td className="border px-4 py-2">
                                                        <Link
                                                            href={area.linkUrl}
                                                            target="_blank"
                                                            rel="noreferrer"
                                                            className="text-blue-600 hover:underline"
                                                        >
                                                            {area.linkUrl}
                                                        </Link>
                                                    </td>
                                                    <td className="py-2 px-4 flex gap-2">
                                                        <div className="flex space-x-2">
                                                            <Link
                                                                href={`/home/<USER>/edit`}
                                                                className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                                                            >
                                                                <Pen className="w-4 h-4" />
                                                            </Link>
                                                            <button
                                                                onClick={() => handleDelete(area.id)}

                                                                className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                                                            >
                                                                <Trash className="w-4 h-4" />
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                ) : (
                                    <p className="mb-6">No hiking areas found.</p>
                                )}
                            </div>
                        </div>
                    ) : (
                        <>
                            <p className="mb-6">No hiking area data found.</p>
                            <button
                                className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition"
                                onClick={() => router.push("/home/<USER>/create")}
                            >
                                Create Hiking Area
                            </button>
                        </>
                    )}
                </div>
            </section>
        </div>
    );
};

export default HikingAreaSection;
