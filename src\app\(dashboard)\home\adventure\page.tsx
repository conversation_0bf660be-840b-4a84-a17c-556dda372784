"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { IAdventure } from '@/types/home';
import { UseGetAdventure } from '@/modules/home/<USER>/queries/use-get-adventure';
import { UseDeleteAdventure } from '@/modules/home/<USER>/mutations/use-delete-adventure';

const DiscoverAdminPage: React.FC = () => {
  const { data: adventureResponse, isLoading, error, refetch } = UseGetAdventure();
  const deleteAdventureMutation = UseDeleteAdventure();

  const entries: IAdventure[] = adventureResponse?.data || [];
  const visibleImages = 2;

  const handleDelete = async (adventure: IAdventure) => {
    if (window.confirm(`Are you sure you want to delete "${adventure.title}"? This action cannot be undone.`)) {
      try {
        await deleteAdventureMutation.mutateAsync(adventure);
      } catch (error) {
        console.error('Failed to delete adventure:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 bg-gray-100 min-h-screen">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-gray-600">Loading adventures...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-gray-100 min-h-screen">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-lg text-red-600 mb-4">Error loading adventures</div>
            <button
              onClick={() => refetch()}
              className="px-4 py-2 bg-brand text-white rounded hover:bg-brand/80"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold">Discover Entries</h1>
        <Link href="/home/<USER>/create">
          <button className="px-4 py-2 bg-brand text-white rounded hover:bg-brand/80">
            + Add Entry
          </button>
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow overflow-x-auto">
        <table className="min-w-full text-left">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Title</th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Images</th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Points</th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Link</th>
              <th className="px-6 py-3"></th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {entries.length > 0 ? (
              entries.map(entry => (
                <tr key={entry.id}>
                  <td className="px-6 py-4 text-sm text-gray-700 font-medium">
                    {entry.title}
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      {entry.images && entry.images.length > 0 ? (
                        entry.images
                          .filter(src => {
                            // Only allow absolute URLs or relative paths starting with '/'
                            return typeof src === 'string' &&
                              (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('/'));
                          })
                          .slice(0, visibleImages)
                          .map((src, idx) => (
                            <Image
                              key={idx}
                              src={src}
                              alt={`img-${entry.id}-${idx}`}
                              width={64}
                              height={64}
                              className="object-cover rounded"
                            />
                          ))
                      ) : (
                        <div className="text-gray-400 text-sm">No images</div>
                      )}

                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-700">
                    {entry.points && entry.points.length > 0 ? (
                      <ul className="list-disc list-inside space-y-1">
                        {entry.points.map((pt, idx) => (
                          <li key={idx}>{pt}</li>
                        ))}
                      </ul>
                    ) : (
                      <span className="text-gray-400">No points</span>
                    )}
                  </td>
                  <td className="px-6 py-4 text-sm">
                    {entry.linkUrl ? (
                      <a
                        href={entry.linkUrl}
                        className="text-blue-600 hover:underline"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {entry.linkLabel || 'View Link'}
                      </a>
                    ) : (
                      <span className="text-gray-400">No link</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Link href={`/home/<USER>/edit/${entry.id}`}>
                      <button
                        className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                        disabled={deleteAdventureMutation.isPending}
                      >
                        Edit
                      </button>
                    </Link>
                    <button
                      onClick={() => handleDelete(entry)}
                      disabled={deleteAdventureMutation.isPending}
                      className={`px-3 py-1 text-white rounded ${deleteAdventureMutation.isPending
                          ? 'bg-gray-400 cursor-not-allowed'
                          : 'bg-red-600 hover:bg-red-700'
                        }`}
                    >
                      {deleteAdventureMutation.isPending ? 'Deleting...' : 'Delete'}
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                  <div className="flex flex-col items-center space-y-3">
                    <div className="text-lg">No adventure entries found</div>
                    <Link href="/home/<USER>/create">
                      <button className="px-4 py-2 bg-brand text-white rounded hover:bg-brand/80">
                        Create Your First Entry
                      </button>
                    </Link>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DiscoverAdminPage;