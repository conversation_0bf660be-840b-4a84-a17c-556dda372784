import { IReview } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function UseGetReview() {
  return useQuery<IApiResponse<IReview>, Error>({
    queryKey: ['review'],
    queryFn: () =>
      fetch(`/api/home-review`, {
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((res) => res.json()),
  });
}
