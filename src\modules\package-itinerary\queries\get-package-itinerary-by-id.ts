import { IPackageItinerary } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetPackageItineraryById(id: string) {
  return useQuery<IApiResponse<IPackageItinerary>, Error>({
    queryKey: ['package', id],
    queryFn: () =>
      fetch(`/api/package-itinerary/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id,
  });
}
