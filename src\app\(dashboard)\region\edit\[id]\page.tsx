"use client";

import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useGetRegionById } from "@/modules/region/queries/use-get-region-by-id";
import { useUpdateRegion } from "@/modules/region/mutation/use-update-region";
import ImageUploadSingleWithMutation from "@/modules/images/components/upload-single-image";

interface EditRegionProps {
  params: Promise<{
    id: string;
  }>;
}

const EditRegion: React.FC<EditRegionProps> = ({ params }) => {
  const { id } = React.use(params);

  const router = useRouter();
  const { data, isLoading, error } = useGetRegionById(id);
  const updateRegion = useUpdateRegion();

  const [name, setName] = useState("");
  const [slug, setSlug] = useState("");
  const [image, setImage] = useState("");
  const [description, setDescription] = useState("");

  useEffect(() => {
    if (data?.data) {
      setName(data.data.name || "");
      setSlug(data.data.slug || "");
      setImage(data.data.image || "");
      setDescription(data.data.description || "");
    }
  }, [data]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!name || !slug) {
      toast.error("Name and slug are required");
      return;
    }
    if (!data?.data) {
      toast.error("Region data is missing");
      return;
    }
    updateRegion.mutate(
      { ...data.data, name, slug, image, description },
      {
        onSuccess: () => {
          toast.success("Region updated successfully!");
          router.push("/region");
        },
        onError: (err: Error) => {
          toast.error(`Failed to update region: ${err.message}`);
        },
      }
    );
  };

  if (isLoading) return <div className="p-6">Loading region...</div>;
  if (error) return <div className="p-6">Error loading region: {error.message}</div>;
  if (!data?.data) return <div className="p-6">Region not found</div>;

  return (
    <div className="p-6 container mx-auto bg-white rounded shadow">
      <h1 className="text-2xl font-bold mb-6">Edit Region</h1>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="name" className="block mb-1 font-medium">
            Name
          </label>
          <input
            id="name"
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="w-full border rounded px-3 py-2"
            required
          />
        </div>
        <div>
          <label htmlFor="slug" className="block mb-1 font-medium">
            Slug
          </label>
          <input
            id="slug"
            type="text"
            value={slug}
            onChange={(e) => setSlug(e.target.value)}
            className="w-full border rounded px-3 py-2"
            required
          />
        </div>

        <div>
          <label className="block mb-1 font-medium">Image</label>
          <ImageUploadSingleWithMutation
            initialUrl={image}
            onUploaded={(url) => setImage(url)}
          />
        </div>

        <div>
          <label htmlFor="description" className="block mb-1 font-medium">
            Description
          </label>
          <textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="w-full border rounded px-3 py-2"
            rows={4}
            required
          ></textarea>
        </div>

        <div className="flex gap-4">
          <button
            type="submit"
            disabled={updateRegion.isPending}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
          >
            {updateRegion.isPending ? "Updating..." : "Update"}
          </button>
          <button
            type="button"
            onClick={() => router.push("/region")}
            className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditRegion;