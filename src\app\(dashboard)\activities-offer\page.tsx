'use client';

import React from 'react';

import { useGetActivity } from '@/modules/activity/queries/use-get-activity';
import { Pen } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

const ActivitiesPage: React.FC = () => {
  const { data, isLoading, error } = useGetActivity();

  if (isLoading) return <p>Loading activities...</p>;
  if (error) return <p>Error loading activities: {error.message}</p>;

  const activities = data?.data ?? [];

  return (
    <div className="p-6">
      <>
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold mb-4">Activities List</h1>
          <Link href="/activities-offer/create">
            <Button className="mb-4 px-4 py-2 rounded border">
              Add Activity
            </Button>
          </Link>
        </div>
        <table className="min-w-full border rounded">
          <thead className="bg-gray-100">
            <tr>
              <th className="p-2 border">SN</th>
              <th className="p-2 border">Name</th>
              <th className="p-2 border">Slug</th>
              <th className="p-2 border">Image</th>
              <th className="p-2 border">Description</th>
              <th className="p-2 border">Actions</th>
            </tr>
          </thead>
          <tbody>
            {activities.map((activity, idx) => (
              <tr
                key={activity.id}
                className={idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'}
              >
                <td className="p-2 border">{idx + 1}</td>
                <td className="p-2 border">{activity.name}</td>
                <td className="p-2 border">{activity.slug}</td>
                <td className="p-2 border">
                  {activity.image ? (
                    <img
                      src={activity.image}
                      alt={activity.name}
                      className="w-16 h-16 object-cover rounded"
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center text-xs text-gray-500">
                      No Image
                    </div>
                  )}
                </td>
                <td className="p-2 border">
                  {activity.description?.trim() ? (
                    activity.description
                  ) : (
                    <em>No description</em>
                  )}
                </td>
                <td className="p-2 border">
                  <div className="flex space-x-2">
                    <Link
                      href={`/activities-offer/edit/${activity.id}`}
                      className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                    >
                      <Pen className="w-4 h-4" />
                    </Link>

                    {/* <button
                        // onClick={handleDelete}
                        className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                        type="button"
                      >
                        <Trash className="w-4 h-4" />
                      </button> */}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </>
    </div>
  );
};

export default ActivitiesPage;
