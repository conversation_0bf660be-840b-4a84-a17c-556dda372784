import { Edit, Trash2 } from 'lucide-react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { IPackageCostDate } from '@/types/package_'

export function CostDateList({
  items,
  onEdit,
  onDelete,
}: {
  items: IPackageCostDate[],
  onEdit: (id: string) => void,
  onDelete: (id: string) => void
}) {
  if (items.length === 0) {
    return (
      <Card>
        <CardHeader><CardTitle>Cost & Date List</CardTitle></CardHeader>
        <CardContent className="text-center text-gray-500">
          No cost/date entries yet.
        </CardContent>
      </Card>
    )
  }
  return (
    <Card>
      <CardHeader><CardTitle>Cost & Date List</CardTitle></CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Days</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>End Date</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Discount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Publish</TableHead>
                <TableHead>Mark Upcoming Treks</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map(item => (
                <TableRow key={item.id}>
                  <TableCell>{item.days}</TableCell>
                  <TableCell>{item.startDate}</TableCell>
                  <TableCell>{item.endDate}</TableCell>
                  <TableCell>{item.price}</TableCell>
                  <TableCell>{item.discountPrice}</TableCell>
                  <TableCell>{item.tripStatus}</TableCell>
                  <TableCell>{item.published ? 'Yes' : 'No'}</TableCell>
                  <TableCell>{item.markUpcomingTreks ? 'Yes' : 'No'}</TableCell>
                  <TableCell>
                    <Button size="sm" variant="outline" onClick={() => onEdit(item.id)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="ml-1 text-red-600" onClick={() => onDelete(item.id)}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
