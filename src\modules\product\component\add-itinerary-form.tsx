'use client'

import { useState, useEffect, FormEvent } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Plus, Edit, Save, X } from 'lucide-react'
import { IPackageItinerary } from '@/types/package_'
import ImageUploadSingleWithMutation from '@/modules/images/components/upload-single-image'

interface AddItineraryFormProps {
  editingItem: IPackageItinerary | null
  onAddItinerary: (item: Omit<IPackageItinerary, 'id' | 'createdAt' | 'updatedAt'>) => void
  onUpdateItinerary: (item: IPackageItinerary) => void
  onCancelEdit: () => void
}

const initialFormState: Omit<IPackageItinerary, 'id' | 'createdAt' | 'updatedAt'> = {
  packageId: '',
  heading: '',
  day: 0,
  title: '',
  activity: '',
  trekDistance: '',
  flightHours: '',
  drivingHours: '',
  highestAltitude: '',
  trekDuration: '',
  image: '',
}

export function AddItineraryForm({
  editingItem,
  onAddItinerary,
  onUpdateItinerary,
  onCancelEdit,
}: AddItineraryFormProps) {
  const [formData, setFormData] = useState(initialFormState)
  const [imageUrl, setImageUrl] = useState<string>(initialFormState.image)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const isEditing = editingItem !== null

  useEffect(() => {
    if (isEditing && editingItem) {
      const { id, createdAt, updatedAt, ...rest } = editingItem
      setFormData(rest)
      setImageUrl(rest.image || '')
    } else {
      setFormData(initialFormState)
      setImageUrl('')
    }
  }, [editingItem, isEditing])

  const handleInputChange = <K extends keyof typeof formData>(field: K, value: typeof formData[K]) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const validateForm = (): boolean => {
    if (!formData.day || formData.day <= 0) {
      alert('Day number is required and must be greater than 0')
      return false
    }
    if (!formData.title.trim()) {
      alert('Title is required')
      return false
    }
    if (!formData.heading.trim()) {
      alert('Heading is required')
      return false
    }
    if (!formData.activity.trim()) {
      alert('Activity is required')
      return false
    }
    return true
  }

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault()
    if (!validateForm()) return

    setIsSubmitting(true)

    try {
      const payload = {
        ...formData,
        image: imageUrl,
      }
      if (isEditing && editingItem) {
        const updatedItem: IPackageItinerary = {
          ...editingItem,
          ...payload,
        }
        onUpdateItinerary(updatedItem)
      } else {
        onAddItinerary(payload)
      }
      setFormData(initialFormState)
      setImageUrl('')
      const fileInput = document.getElementById('image') as HTMLInputElement | null
      if (fileInput) fileInput.value = ''
    } catch (error) {
      console.error('Error saving itinerary item:', error)
      alert('Error saving itinerary item. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    setFormData(initialFormState)
    setImageUrl('')
    const fileInput = document.getElementById('image') as HTMLInputElement | null
    if (fileInput) fileInput.value = ''
    onCancelEdit()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isEditing ? <Edit className="w-5 h-5" /> : <Plus className="w-5 h-5" />}
          {isEditing ? 'Edit Package Itinerary' : 'Add Package Itinerary'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Heading */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="heading" className="mb-2">Heading *</Label>
              <Input
                id="heading"
                value={formData.heading}
                onChange={(e) => handleInputChange('heading', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="Enter heading"
                required
              />
            </div>

            {/* Day */}
            <div>
              <Label htmlFor="day" className="mb-2">Day Number *</Label>
              <Input
                id="day"
                type="number"
                value={formData.day}
                onChange={(e) => handleInputChange('day', Number(e.target.value))}
                className="border-black/20 rounded-none"
                placeholder="e.g., 1, 2, 3"
                min={1}
                required
              />
            </div>
          </div>

          {/* Title */}
          <div>
            <Label htmlFor="title" className="mb-2">Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="Enter activity title"
              required
            />
          </div>

          {/* Activity */}
          <div>
            <Label htmlFor="activity" className="mb-2">Activity *</Label>
            <Input
              id="activity"
              value={formData.activity}
              onChange={(e) => handleInputChange('activity', e.target.value)}
              className="border-black/20 rounded-none"
              placeholder="Enter activity"
              required
            />
          </div>

          {/* Trek info */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="trekDistance" className="mb-2">Trek Distance</Label>
              <Input
                id="trekDistance"
                value={formData.trekDistance}
                onChange={(e) => handleInputChange('trekDistance', e.target.value)}
                className="border-black/20 rounded-none"
                required
                placeholder="e.g., 5km"
              />
            </div>

            <div>
              <Label htmlFor="flightHours" className="mb-2">Flight Hours</Label>
              <Input
                id="flightHours"
                value={formData.flightHours}
                onChange={(e) => handleInputChange('flightHours', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., 1.5"
                required
              />
            </div>

            <div>
              <Label htmlFor="drivingHours" className="mb-2">Driving Hours</Label>
              <Input
                id="drivingHours"
                value={formData.drivingHours}
                onChange={(e) => handleInputChange('drivingHours', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., 3"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="highestAltitude" className="mb-2">Highest Altitude</Label>
              <Input
                id="highestAltitude"
                value={formData.highestAltitude}
                onChange={(e) => handleInputChange('highestAltitude', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., 4000m"
                required
              />
            </div>

            <div>
              <Label htmlFor="trekDuration" className="mb-2">Trek Duration</Label>
              <Input
                id="trekDuration"
                value={formData.trekDuration}
                onChange={(e) => handleInputChange('trekDuration', e.target.value)}
                className="border-black/20 rounded-none"
                placeholder="e.g., 6 hours"
                required
              />
            </div>
          </div>

          {/* Image Upload */}
          <div>
            <Label htmlFor="image" className="mb-2">Image</Label>
            <ImageUploadSingleWithMutation
              initialUrl={imageUrl}
              onUploaded={(url) => setImageUrl(url)}
              label="Upload Image"
            />
          </div>

          {/* Form buttons */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            {isEditing && (
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isSubmitting}
                className="flex items-center gap-2"
              >
                <X className="w-4 h-4" />
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex items-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  {isEditing ? 'Saving...' : 'Adding...'}
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  {isEditing ? 'Save Changes' : 'Add Itinerary'}
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
