'use client';

import React, { useState, useEffect, ChangeEvent } from 'react';
import { IExperience, IExperienceFeature } from '@/types/home';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { UseGetExperience } from '../queries/use-get-experience';
import { UseUpdateExperience } from '../mutations/use-update-experience';
import { useGetHome } from '../../queries/get-home';

const EditExperiencePage: React.FC = () => {
  const router = useRouter();
  const { data: homeData } = useGetHome();
  const { data, isLoading, error } = UseGetExperience();
  const updateExperience = UseUpdateExperience();

  const [experience, setExperience] = useState<IExperience | null>(null);

  useEffect(() => {
    if (data?.data) {
      setExperience(data.data);
    }
  }, [data]);

  useEffect(() => {
    const homeId = homeData?.data?.id;
    if (homeId && experience && !experience.homeId) {
      setExperience((prev) => prev && { ...prev, homeId });
    }
  }, [homeData, experience]);

  const handleHeadingChange = (e: ChangeEvent<HTMLInputElement>) => {
    setExperience((prev) => prev && { ...prev, heading: e.target.value });
  };

  const handleSubHeadingChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setExperience((prev) => prev && { ...prev, subHeading: e.target.value });
  };

  const handleFeatureChange = (
    index: number,
    field: keyof IExperienceFeature,
    value: string
  ) => {
    setExperience((prev) => {
      if (!prev) return prev;
      const features = [...prev.features];
      features[index] = { ...features[index], [field]: value };
      return { ...prev, features };
    });
  };

  const handleAddFeature = () => {
    setExperience((prev) => {
      if (!prev) return prev;
      return {
        ...prev,
        features: [...prev.features, { id: '', title: '', subtitle: '' }],
      };
    });
  };

  const handleDeleteFeature = (index: number) => {
    setExperience((prev) => {
      if (!prev) return prev;
      return { ...prev, features: prev.features.filter((_, i) => i !== index) };
    });
  };

  const handleSave = () => {
    if (experience && experience.id) {
      updateExperience.mutate(experience, {
        onSuccess: () => {
          router.push('/home/<USER>');
        },
        onError: (err) => {
          alert(`Update failed: ${err.message}`);
        },
      });
    } else {
      alert('Experience ID missing, cannot update.');
    }
  };

  if (isLoading || !experience) return <p>Loading experience...</p>;
  if (error) return <p>Error loading experience: {error.message}</p>;

  return (
    <div className="p-6 container mx-auto space-y-6">
      <h1 className="text-3xl font-bold mb-4">Edit Experience</h1>

      <div>
        <label className="block mb-1 font-medium">Heading</label>
        <input
          type="text"
          value={experience.heading}
          onChange={handleHeadingChange}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <label className="block mb-1 font-medium">Subheading</label>
        <textarea
          value={experience.subHeading}
          onChange={handleSubHeadingChange}
          rows={3}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-3">Features</h2>
        {experience.features.map((feature, index) => (
          <div key={index} className="mb-4 border rounded p-4 bg-white">
            <input
              type="text"
              placeholder="Feature Title"
              value={feature.title}
              onChange={(e) =>
                handleFeatureChange(index, 'title', e.target.value)
              }
              className="w-full mb-2 rounded border px-3 py-2"
            />
            <textarea
              placeholder="Feature Subtitle"
              value={feature.subtitle}
              onChange={(e) =>
                handleFeatureChange(index, 'subtitle', e.target.value)
              }
              rows={2}
              className="w-full rounded border px-3 py-2"
            />
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDeleteFeature(index)}
              className="mt-2"
            >
              Delete Feature
            </Button>
          </div>
        ))}
        {experience.features.length < 3 && (
          <Button onClick={handleAddFeature} className="mt-3">
            + Add Feature
          </Button>
        )}
      </div>

      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          className="bg-brand text-white hover:bg-brand/80"
        >
          Update Experience
        </Button>
      </div>
    </div>
  );
};

export default EditExperiencePage;
