import { IExperience } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function UseGetExperience() {
  return useQuery<IApiResponse<IExperience>, Error>({
    queryKey: ['experience'],
    queryFn: () =>
      fetch(`/api/home-experience`, {
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((res) => res.json()),
  });
}
