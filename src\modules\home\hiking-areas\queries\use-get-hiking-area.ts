import { IHikingArea } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function UseGetHikingArea() {
  return useQuery<IApiResponse<IHikingArea>, Error>({
    queryKey: ['hiking-area'],
    queryFn: () =>
      fetch(`/api/home-hiking-areas`, {
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((res) => res.json()),
  });
}
