
import { Edit, Trash2 } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { IPackageGroupPrice } from '@/types/package_'

export function GroupDiscountList({
  items,
  onEdit,
  onDelete,
}: {
  items: IPackageGroupPrice[],
  onEdit: (id: string) => void,
  onDelete: (id: string) => void
}) {
  if (items.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Group Discount List</CardTitle>
        </CardHeader>
        <CardContent className="text-center text-gray-500">
          You do not have any group discount tiers yet.
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Group Discount List</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>No Of Person</TableHead>
                <TableHead>Price Per Person</TableHead>
                <TableHead>Note</TableHead>
                <TableHead>Publish</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map((item, index) => (
                <TableRow key={item.id || `item-${index}`}>
                  <TableCell>{item.numberOfPeople}</TableCell>
                  <TableCell>{item.pricePerPerson}</TableCell>
                  <TableCell>{item.note}</TableCell>
                  <TableCell>{item.published ? "Yes" : "No"}</TableCell>
                  <TableCell>
                    <Button size="sm" variant="outline" onClick={() => onEdit(item.id)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="ml-1 text-red-600" onClick={() => onDelete(item.id)}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
