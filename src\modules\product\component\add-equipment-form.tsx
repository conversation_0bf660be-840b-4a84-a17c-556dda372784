'use client'

import { useState, useEffect, FormEvent } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Plus, Edit, X } from 'lucide-react'
import dynamic from 'next/dynamic'
import { IPackageEquipment } from "@/types/package_"
const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false })

const initialEquipmentState = {
  packageId: '',
  id: '',
  title: '',
  description: '',
  head: '',
  face: '',
  body: '',
}

export function AddEquipmentForm({
  editingEquipment,
  onAddEquipment,
  onUpdateEquipment,
  onCancelEdit
}: {
  editingEquipment: IPackageEquipment | null,
  onAddEquipment: (data: Omit<IPackageEquipment, 'id'>) => void,
  onUpdateEquipment: (data: IPackageEquipment) => void,
  onCancelEdit: () => void
}) {
  const [formData, setFormData] = useState(initialEquipmentState)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const isEditing = editingEquipment !== null

  useEffect(() => {
    if (isEditing && editingEquipment) {
      setFormData({
        id: editingEquipment.id,
        packageId: editingEquipment.packageId,
        title: editingEquipment.title,
        description: editingEquipment.description,
        head: editingEquipment.head,
        face: editingEquipment.face,
        body: editingEquipment.body,
      })
    } else {
      setFormData(initialEquipmentState)
    }
  }, [editingEquipment, isEditing])

  const handleInputChange = (field: keyof typeof initialEquipmentState, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const validateForm = () => {
    if (!formData.title.trim()) {
      alert('Title is required')
      return false
    }
    return true
  }

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault()
    if (!validateForm()) return
    setIsSubmitting(true)
    try {
      if (isEditing && editingEquipment) {
        onUpdateEquipment({ ...editingEquipment, ...formData })
      } else {
        onAddEquipment({ ...formData })
      }
      setFormData(initialEquipmentState)
    } catch (error) {
      console.error('Error adding equipment:', error)
      alert('Error adding equipment. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isEditing ? <Edit className="w-5 h-5" /> : <Plus className="w-5 h-5" />}
          {isEditing ? 'Edit Package Equipment' : 'Add Package Equipment'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="title" className="mb-2">Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={e => handleInputChange('title', e.target.value)}
              className="border-black/20 rounded-none"
              required
              placeholder="Enter equipment title"
            />
          </div>
          <div>
            <Label htmlFor="description" className="mb-2">Description</Label>
            <Input
              id="description"
              value={formData.description}
              onChange={e => handleInputChange('description', e.target.value)}
              className="border-black/20 rounded-none"
              placeholder="Short description"
            />
          </div>
          <div>
            <Label className="mb-2" >Head</Label>
            <RichTextEditor
              value={formData.head}
              onChange={data => handleInputChange('head', data)}
            />
          </div>
          <div>
            <Label className="mb-2">Face</Label>
            <RichTextEditor
              value={formData.face}
              onChange={data => handleInputChange('face', data)}
            />
          </div>
          <div>
            <Label className="mb-2">Body</Label>
            <RichTextEditor
              value={formData.body}
              onChange={data => handleInputChange('body', data)}
            />
          </div>
          <div className="flex justify-end gap-2 pt-4 border-t">
            {isEditing && (
              <Button type="button" variant="outline" onClick={onCancelEdit} disabled={isSubmitting}>
                <X className="w-4 h-4" /> Cancel
              </Button>
            )}
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : isEditing ? 'Save Changes' : 'Add Equipment'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
