import { IExperience } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function UseUpdateExperience() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IExperience>, Error, IExperience>({
    mutationFn: (data: IExperience) =>
      fetch(`/api/home-experience/${data.id}`, {
        method: 'PATCH',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['experience'] });
      toast.success('Experience Updated Sucessfully');
    },
    onError: () => {
      toast.error('Error Updating Experience');
    },
  });
}
