export interface IApiResponse<T> {
  statusCode: number;
  success: boolean;
  data?: T;
  path?: string;
  message: string;
  meta?: {
    total: number;
    items: number;
    currentPage: number;
    perPage: number;
    lastPage: number;
  };
}

export interface IApiResponseWithPagination<T> {
  statusCode: number;
  success: boolean;
  data: T[];
  path: string;
  message: string;
  meta: {
    total: number;
    items: number;
    currentPage: number;
    perPage: number;
    lastPage: number;
  };
}
