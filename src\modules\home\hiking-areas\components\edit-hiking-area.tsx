"use client";

import React, { useState, useEffect, ChangeEvent } from "react";
import { IHikingArea, IHikingAreaItem } from "@/types/home";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { UseGetHikingArea } from "../queries/use-get-hiking-area";
import { UseUpdateHikingArea } from "../mutations/use-update-hiking-area";
import { toast } from "sonner";
import { useGetHome } from "../../queries/get-home";
import ImageUploadSingleWithMutation from "@/modules/images/components/upload-single-image";

const EditHikingAreaPage: React.FC = () => {
  const router = useRouter();
  const { data: homeData } = useGetHome();
  const { data, isLoading, error } = UseGetHikingArea();
  const updateHikingArea = UseUpdateHikingArea();

  const [hikingArea, setHikingArea] = useState<IHikingArea | null>(null);
  const homeId = homeData?.data?.id ?? "";

  useEffect(() => {
    if (data?.data) {
      setHikingArea(data.data);
    }
  }, [data]);

  useEffect(() => {
    if (homeId && hikingArea && !hikingArea.homeId) {
      setHikingArea(prev => prev && { ...prev, homeId });
    }
  }, [homeId, hikingArea]);

  // Main heading change handler
  const handleHeadingChange = (e: ChangeEvent<HTMLInputElement>) => {
    setHikingArea(prev => prev && { ...prev, heading: e.target.value });
  };

  // Subheading change handler
  const handleSubHeadingChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setHikingArea(prev => prev && { ...prev, subHeading: e.target.value });
  };

  // Nested area field update handler
  const handleAreaChange = (index: number, field: keyof IHikingAreaItem, value: string) => {
    setHikingArea(prev => {
      if (!prev) return prev;
      const updatedAreas = [...prev.areas];
      updatedAreas[index] = { ...updatedAreas[index], [field]: value };
      return { ...prev, areas: updatedAreas };
    });
  };

  // Add new empty hiking area
  const handleAddArea = () => {
    setHikingArea(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        areas: [
          ...prev.areas,
          {
            id: crypto.randomUUID(),
            homeHikingId: homeId,
            title: "",
            subtitle: "",
            image: "",
            linkUrl: "",
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ],
      };
    });
  };

  const handleDeleteArea = (index: number) => {
    setHikingArea(prev => {
      if (!prev) return prev;
      return { ...prev, areas: prev.areas.filter((_, i) => i !== index) };
    });
  };

  const handleSave = () => {
    if (!hikingArea || !hikingArea.id) {
      toast.error("Invalid hiking area data");
      return;
    }
    updateHikingArea.mutate(hikingArea, {
      onSuccess: () => {
        toast.success("Hiking area updated successfully");
        router.push("/hiking-areas");
      },
      onError: (err) => {
        toast.error(`Update failed: ${err.message}`);
      },
    });
  };

  if (isLoading || !hikingArea) return <p>Loading hiking area...</p>;
  if (error) return <p>Error loading hiking area: {error.message}</p>;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold">Edit Hiking Area</h1>

      <div>
        <label className="block mb-1 font-medium">Heading</label>
        <input
          type="text"
          value={hikingArea.heading}
          onChange={handleHeadingChange}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <label className="block mb-1 font-medium">Subheading</label>
        <textarea
          value={hikingArea.subHeading}
          onChange={handleSubHeadingChange}
          rows={3}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-4">Hiking Areas</h2>
        {hikingArea.areas.map((area, index) => (
          <div
            key={area.id}
            className="mb-4 border p-4 rounded bg-white shadow-sm space-y-3"
          >
            <div className="flex justify-between items-center">
              <h3 className="font-semibold">{area.title || "(No Title)"}</h3>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => handleDeleteArea(index)}
              >
                Delete
              </Button>
            </div>

            <input
              type="text"
              placeholder="Title"
              value={area.title}
              onChange={(e) => handleAreaChange(index, "title", e.target.value)}
              required
              className="w-full rounded border px-3 py-2"
            />
            <input
              type="text"
              placeholder="Subtitle"
              value={area.subtitle}
              onChange={(e) => handleAreaChange(index, "subtitle", e.target.value)}
              className="w-full rounded border px-3 py-2"
            />

            <ImageUploadSingleWithMutation
              label="Upload Area Image"
              initialUrl={
                area.image && area.image.includes("example.com")
                  ? "/images/placeholder.jpg"
                  : area.image || ""
              }
              onUploaded={(url) => handleAreaChange(index, "image", url)}
            />

            <input
              type="text"
              placeholder="Link URL"
              value={area.linkUrl}
              onChange={(e) => handleAreaChange(index, "linkUrl", e.target.value)}
              className="w-full rounded border px-3 py-2"
            />
          </div>
        ))}

        <Button onClick={handleAddArea} className="mt-3">
          + Add Hiking Area
        </Button>
      </div>

      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.push("/hiking-areas")}
        >
          Cancel
        </Button>
        <Button onClick={handleSave} className="bg-brand text-white hover:bg-brand/80">
          Update Hiking Area
        </Button>
      </div>
    </div>
  );
};

export default EditHikingAreaPage;
