'use client';

import {
  createContext,
  useContext,
  useState,
  useEffect,
  type ReactNode,
} from 'react';
import { useRouter } from 'next/navigation';
import { useCheckLoginStatus } from '@/modules/auth/queries/user-status';
import User, { LoginResponse } from '@/types/user';
import { useLoginMutation } from '@/modules/auth/mutations/login-mutation';

type AuthContextType = {
  user: User | null;
  login: (username: string, password: string) => void;
  logout: () => void;
  isLoading: boolean;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const { data: userData, isLoading: isLoadingStatus } = useCheckLoginStatus();
  const handleLoginSuccess = (response: LoginResponse) => {
    console.log('User logged in:', user);
    setUser(response.data);
    setIsLoading(false);
    router.push('/');
  };
  const loginMutation = useLoginMutation(handleLoginSuccess);

  useEffect(() => {
    console.log('User data:', userData);
    setUser(userData?.data || null);
    setIsLoading(isLoadingStatus);
  }, [isLoadingStatus, userData]);

  const login = (username: string, password: string) => {
    loginMutation.mutate({
      email: username,
      password,
    });
  };

  const logout = () => {
    //set cookie
    document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    setUser(null);
    router.push('/login');
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
