import { IPackage } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetPackages(params?: {
  page?: number;
  limit?: number;
  sort?: string;
  searchFields?: string;
  search?: string;
}) {
  return useQuery<IApiResponse<IPackage[]>, Error>({
    queryKey: ['packages', params],
    queryFn: () =>
      fetch(
        `/api/package?${new URLSearchParams(params as Record<string, string>)}`,
        {
          mode: 'cors',
          credentials: 'include',
        }
      ).then((res) => res.json()),
  });
}
