'use client';

import { useState } from 'react';
import { use } from 'react';
import { ItineraryItem } from '@/types/package-form';
import { AddItineraryForm } from '@/modules/product/component/add-itinerary-form';
import { ItineraryList } from '@/modules/product/component/itinerary-list';
import { EditTabs } from '@/modules/product/component/edit-tabs';
import { Button } from '@/components/ui/button';

interface EditItineraryPageProps {
  params: Promise<{ id: string }>;
}

export default function EditItineraryPage({ params }: EditItineraryPageProps) {
  const { id } = use(params);
  const packageId = parseInt(id);

  const [items, setItems] = useState<ItineraryItem[]>([]);
  const [editingItem, setEditingItem] = useState<ItineraryItem | null>(null);

  const handleAddItinerary = (newItem: Omit<ItineraryItem, 'id'>) => {
    setItems((prev) => [...prev, { ...newItem, id: Date.now() }]);
  };
  const handleUpdateItinerary = (updatedItem: ItineraryItem) => {
    setItems((prev) =>
      prev.map((item) => (item.id === updatedItem.id ? updatedItem : item))
    );
    setEditingItem(null);
  };
  const handleDeleteItinerary = (id: number) => {
    setItems((prev) => prev.filter((i) => i.id !== id));
  };
  const handleEditItinerary = (id: number) => {
    const found = items.find((i) => i.id === id) || null;
    setEditingItem(found);
  };
  const handleCancelEdit = () => setEditingItem(null);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <EditTabs packageId={packageId} />
      <div className="grid grid-cols-2 gap-4">
        {/* <AddItineraryForm
                    editingItem={editingItem}
                    onAddItinerary={handleAddItinerary}
                    onUpdateItinerary={handleUpdateItinerary}
                    onCancelEdit={handleCancelEdit}
                />
                <ItineraryList items={items} onEdit={handleEditItinerary} onDelete={handleDeleteItinerary} /> */}
      </div>
      <div>
        <Button className="mt-9 px-4 py-2 bg-brand text-white rounded-lg">
          Save
        </Button>
      </div>
    </div>
  );
}
