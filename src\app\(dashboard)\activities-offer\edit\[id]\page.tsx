'use client';

import React from 'react';
import { useRouter, useParams } from 'next/navigation';
import { toast } from 'sonner';
import { useGetActivity } from '@/modules/activity/queries/use-get-activity';
import EditActivity from '@/modules/activity/components/edit-activity';
import { IActivity } from '@/types/package_';

const EditActivityPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const { id } = params;

  const { data, isLoading, error } = useGetActivity();

  if (isLoading) return <p>Loading activities...</p>;
  if (error) {
    toast.error('Failed to load activities');
    router.push('/activities-offer');
    return null;
  }

  const activities: IActivity[] = data?.data ?? [];
  const activity = activities.find((a) => a.id === id);

  if (!activity) return <p>Activity not found.</p>;

  return <EditActivity initialActivity={activity} />;
};

export default EditActivityPage;
