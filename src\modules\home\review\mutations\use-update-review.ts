import { IReview } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function UseUpdateReview() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IReview>, Error, IReview>({
    mutationFn: (data: IReview) =>
      fetch(`/api/home-review/${data.id}`, {
        method: 'PATCH',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['review'] });
      toast.success('Review Updated Sucessfully');
    },
    onError: () => {
      toast.error('Error Updating Review');
    },
  });
}
