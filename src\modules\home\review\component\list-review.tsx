'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { UseGetReview } from '../queries/use-get-review';
import Image from 'next/image';

const ListReviews: React.FC = () => {
  const { data, isLoading, error } = UseGetReview();
  const router = useRouter();

  if (isLoading) return <p>Loading reviews...</p>;
  if (error) return <p>Error loading reviews: {error.message}</p>;

  const reviewBlock = data?.data;

  return (
    <div className="p-6 container mx-auto">
      {reviewBlock ? (
        <>
          <h1 className="text-3xl font-bold mb-4">{reviewBlock.title}</h1>
          <p className="mb-6">{reviewBlock.subtitle}</p>

          {reviewBlock.reviews.length > 0 ? (
            <table className="min-w-full border border-gray-300 mb-6">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border px-4 py-2">Image</th>
                  <th className="border px-4 py-2">Quote</th>
                  <th className="border px-4 py-2">Name</th>
                  <th className="border px-4 py-2">Destination</th>
                  <th className="border px-4 py-2">Date</th>
                </tr>
              </thead>
              <tbody>
                {reviewBlock.reviews.map((review) => (
                  <tr key={review.id} className="even:bg-gray-50">
                    <td className="border px-4 py-2">
                      {review.image && !review.image.includes('example.com') ? (
                        <Image
                          src={review.image}
                          alt={review.name}
                          width={64}
                          height={64}
                          className="w-16 h-16 object-cover rounded"
                        />
                      ) : (
                        <Image
                          src="/images/placeholder.jpg"
                          alt="placeholder"
                          width={64}
                          height={64}
                          className="w-16 h-16 object-cover rounded"
                        />
                      )}
                    </td>
                    <td className="border px-4 py-2">{review.quote}</td>
                    <td className="border px-4 py-2">{review.name}</td>
                    <td className="border px-4 py-2">{review.quote}</td>
                    <td className="border px-4 py-2">{review.date}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <p className="mb-6">No reviews found.</p>
          )}

          <div className="flex space-x-4 justify-center">
            <button
              className="bg-brand text-white px-4 py-2 rounded hover:bg-brand/80"
              onClick={() => router.push('/home/<USER>/edit')}
            >
              Edit Reviews
            </button>
          </div>
        </>
      ) : (
        <p>No review data found. Please create one.</p>
      )}
    </div>
  );
};

export default ListReviews;
