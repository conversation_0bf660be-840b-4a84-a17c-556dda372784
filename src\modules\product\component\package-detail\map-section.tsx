import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { IPackageMap } from "@/types/package_";
import ImageUploadSingleWithMutation from "@/modules/images/components/upload-single-image";

interface PackageMapFormProps {
  map?: Partial<IPackageMap>;
  onMapChange: (data: Partial<IPackageMap>) => void;
}

export function PackageMapForm({ map, onMapChange }: PackageMapFormProps) {
  const handleChange = (field: keyof IPackageMap, value: string) => {
    onMapChange({ ...map, [field]: value });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Trek Route Map</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Label>Title</Label>
        <Input
          value={map?.title || ""}
          onChange={e => handleChange("title", e.target.value)}
          placeholder="Map title"
        />
        <Label>Map Image</Label>
        <ImageUploadSingleWithMutation
          label="Map Image"
          accept="image/*"
          initialUrl={map?.map || ""}
          onUploaded={url => handleChange("map", url)}
        />
      </CardContent>
    </Card>
  );
}
