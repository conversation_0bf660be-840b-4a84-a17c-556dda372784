"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { UseGetTailoredAdventure } from "../queries/use-get-tailored-adv";

const ListTailoredAdventure: React.FC = () => {
  const { data, isLoading, error } = UseGetTailoredAdventure();
  const router = useRouter();

  if (isLoading) return <p>Loading tailored adventure...</p>;
  if (error) return <p>Error loading tailored adventure: {error.message}</p>;

  const tailored = data?.data;

  return (
    <div className="p-6 container mx-auto text-center">
      {tailored ? (
        <>
          <h1 className="text-3xl font-bold mb-4">Heading: {tailored.title}</h1>
          <p className="mb-6">SubHeading: {tailored.subtitle}</p>

          {tailored.features.length > 0 && (
            <table className="min-w-full border border-gray-300 mb-6">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border px-4 py-2">Feature Title</th>
                  <th className="border px-4 py-2">Feature Subtitle</th>
                  <th className="border px-4 py-2">Link Label</th>
                  <th className="border px-4 py-2">Link URL</th>
                </tr>
              </thead>
              <tbody>
                {tailored.features.map((feature) => (
                  <tr key={feature.id} className="even:bg-gray-50">
                    <td className="border px-4 py-2">{feature.title}</td>
                    <td className="border px-4 py-2">{feature.subtitle}</td>
                    <td className="border px-4 py-2">{feature.linkLabel}</td>
                    <td className="border px-4 py-2">
                      <a href={feature.linkUrl} target="_blank" rel="noreferrer" className="text-blue-600 hover:underline">
                        {feature.linkUrl}
                      </a>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}

          <button
            className="bg-brand text-white px-4 py-2 rounded hover:bg-brand/80"
            onClick={() => router.push("/home/<USER>/edit")}
          >
            Edit Tailored Adventure
          </button>
        </>
      ) : (
        <>
          <p className="mb-6">No tailored adventure data found.</p>
          <button
            className="bg-brand text-white px-4 py-2 rounded hover:bg-brand/80"
            onClick={() => router.push("/home/<USER>/create")}
          >
            Create Tailored Adventure
          </button>
        </>
      )}
    </div>
  );
};

export default ListTailoredAdventure;



// "use client";

// import React, { useState, useCallback, ChangeEvent } from "react";
// import {
//   Dialog,
//   DialogTrigger,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
//   DialogDescription,
//   DialogFooter,
// } from "@/components/ui/dialog";
// import { Button } from "@/components/ui/button";

// interface FeatureEntry {
//   id: number;
//   title: string;
//   subtitle: string;
//   buttonText: string;
//   buttonLink: string;
// }

// const initialEntries: FeatureEntry[] = [
//   {
//     id: 1,
//     title: "EXPLORE PLACES YOU COULDN'T YOURSELF",
//     subtitle:
//       "All trips are led by certified expert guides, unlocking life experiences in places most never see.",
//     buttonText: "Start Your Adventure",
//     buttonLink: "/start",
//   },
//   {
//     id: 2,
//     title: "JOIN A SMALL LIKE-MINDED GROUP",
//     subtitle:
//       "75% join our trips as solo travellers, with most in their 30s–50s. 95% give our group dynamic 5 stars.",
//     buttonText: "Find Your Adventure",
//     buttonLink: "/find",
//   },
//   {
//     id: 3,
//     title: "HASSLE-FREE FROM START TO FINISH",
//     subtitle:
//       "We’ve sorted the logistics, so you can just rock up and have a blast in the wild.",
//     buttonText: "Push Your Adventure",
//     buttonLink: "/push",
//   },
// ];

// export default function TailoredSectionPage() {
//   const [sectionHeading, setSectionHeading] = useState(
//     "Tailored for Every Adventurer"
//   );
//   const [sectionSubheading, setSectionSubheading] = useState(
//     "Pick your adventure style and we'll make it happen."
//   );
//   const [tempHeading, setTempHeading] = useState(sectionHeading);
//   const [tempSubheading, setTempSubheading] = useState(sectionSubheading);
//   const [isSectionEditOpen, setIsSectionEditOpen] = useState(false);

//   const [entries, setEntries] = useState<FeatureEntry[]>(initialEntries);
//   const [isEntryEditOpen, setIsEntryEditOpen] = useState(false);
//   const [currentEntry, setCurrentEntry] = useState<FeatureEntry | null>(
//     null
//   );
//   const [tempTitle, setTempTitle] = useState("");
//   const [tempSubtitle, setTempSubtitle] = useState("");
//   const [tempButtonText, setTempButtonText] = useState("");
//   const [tempButtonLink, setTempButtonLink] = useState("");

//   const openSectionEdit = useCallback(() => {
//     setTempHeading(sectionHeading);
//     setTempSubheading(sectionSubheading);
//     setIsSectionEditOpen(true);
//   }, [sectionHeading, sectionSubheading]);

//   const saveSectionEdit = useCallback(() => {
//     setSectionHeading(tempHeading);
//     setSectionSubheading(tempSubheading);
//     setIsSectionEditOpen(false);
//   }, [tempHeading, tempSubheading]);

//   const openEntryEdit = useCallback(
//     (entry: FeatureEntry | null) => {
//       setCurrentEntry(entry);
//       setTempTitle(entry?.title ?? "");
//       setTempSubtitle(entry?.subtitle ?? "");
//       setTempButtonText(entry?.buttonText ?? "");
//       setTempButtonLink(entry?.buttonLink ?? "");
//       setIsEntryEditOpen(true);
//     },
//     []
//   );

//   const saveEntryEdit = useCallback(() => {
//     if (currentEntry) {
//       setEntries((prev) =>
//         prev.map((e) =>
//           e.id === currentEntry.id
//             ? {
//                 ...e,
//                 title: tempTitle,
//                 subtitle: tempSubtitle,
//                 buttonText: tempButtonText,
//                 buttonLink: tempButtonLink,
//               }
//             : e
//         )
//       );
//     } else {
//       const nextId =
//         entries.length > 0
//           ? Math.max(...entries.map((e) => e.id)) + 1
//           : 1;
//       setEntries((prev) => [
//         ...prev,
//         {
//           id: nextId,
//           title: tempTitle,
//           subtitle: tempSubtitle,
//           buttonText: tempButtonText,
//           buttonLink: tempButtonLink,
//         },
//       ]);
//     }
//     setIsEntryEditOpen(false);
//   }, [
//     currentEntry,
//     entries,
//     tempTitle,
//     tempSubtitle,
//     tempButtonText,
//     tempButtonLink,
//   ]);

//   const handleDelete = useCallback((id: number) => {
//     setEntries((prev) => prev.filter((e) => e.id !== id));
//   }, []);

//   return (
//     <div className="p-6 bg-gray-100 min-h-screen space-y-6">
//       <div className="flex items-center justify-between">
//         <div>
//           <h1 className="text-3xl font-bold">{sectionHeading}</h1>
//           <p className="text-gray-600 mt-1">{sectionSubheading}</p>
//         </div>
//         <Dialog
//           open={isSectionEditOpen}
//           onOpenChange={setIsSectionEditOpen}
//         >
//           <DialogTrigger asChild>
//             <Button className='bg-brand text-white hover:bg-brand/80 hover:text-white' onClick={openSectionEdit}>Edit Heading</Button>
//           </DialogTrigger>
//           <DialogContent>
//             <DialogHeader>
//               <DialogTitle>Edit Section Heading</DialogTitle>
//               <DialogDescription>
//                 Update the main heading and subheading.
//               </DialogDescription>
//             </DialogHeader>
//             <div className="space-y-4 mt-2">
//               <div>
//                 <label className="block font-medium mb-1">Heading</label>
//                 <input
//                   type="text"
//                   value={tempHeading}
//                   onChange={(e: ChangeEvent<HTMLInputElement>) =>
//                     setTempHeading(e.target.value)
//                   }
//                   className="w-full border rounded px-3 py-2"
//                 />
//               </div>
//               <div>
//                 <label className="block font-medium mb-1">
//                   Subheading
//                 </label>
//                 <textarea
//                   value={tempSubheading}
//                   onChange={(e: ChangeEvent<HTMLTextAreaElement>) =>
//                     setTempSubheading(e.target.value)
//                   }
//                   className="w-full border rounded px-3 py-2"
//                   rows={2}
//                 />
//               </div>
//             </div>
//             <DialogFooter>
//               <Button
//                 variant="outline"
//                 onClick={() => setIsSectionEditOpen(false)}
//               >
//                 Cancel
//               </Button>
//               <Button className='bg-brand text-white hover:bg-brand/80 hover:text-white' onClick={saveSectionEdit}>Save</Button>
//             </DialogFooter>
//           </DialogContent>
//         </Dialog>
//       </div>

//       <div className="flex items-center justify-between">
//         <h2 className="text-2xl font-semibold">Features</h2>
//         {entries.length < 3 && (
//           <Button className='bg-brand text-white hover:bg-brand/80 hover:text-white' onClick={() => openEntryEdit(null)}>
//             + Add Feature
//           </Button>
//         )}
//       </div>

//       <Dialog
//         open={isEntryEditOpen}
//         onOpenChange={setIsEntryEditOpen}
//       >
//         <DialogContent>
//           <DialogHeader>
//             <DialogTitle>
//               {currentEntry
//                 ? `Edit Feature #${currentEntry.id}`
//                 : "Add Feature"}
//             </DialogTitle>
//             <DialogDescription>
//               {currentEntry
//                 ? "Modify feature details."
//                 : "Enter details for new feature."}
//             </DialogDescription>
//           </DialogHeader>
//           <div className="space-y-4 mt-2">
//             <div>
//               <label className="block font-medium mb-1">Title</label>
//               <input
//                 type="text"
//                 value={tempTitle}
//                 onChange={(e: ChangeEvent<HTMLInputElement>) =>
//                   setTempTitle(e.target.value)
//                 }
//                 className="w-full border rounded px-3 py-2"
//               />
//             </div>
//             <div>
//               <label className="block font-medium mb-1">
//                 Subtitle
//               </label>
//               <textarea
//                 value={tempSubtitle}
//                 onChange={(e: ChangeEvent<HTMLTextAreaElement>) =>
//                   setTempSubtitle(e.target.value)
//                 }
//                 className="w-full border rounded px-3 py-2"
//                 rows={2}
//               />
//             </div>
//             <div>
//               <label className="block font-medium mb-1">
//                 Button Text
//               </label>
//               <input
//                 type="text"
//                 value={tempButtonText}
//                 onChange={(e: ChangeEvent<HTMLInputElement>) =>
//                   setTempButtonText(e.target.value)
//                 }
//                 className="w-full border rounded px-3 py-2"
//               />
//             </div>
//             <div>
//               <label className="block font-medium mb-1">
//                 Button Link
//               </label>
//               <input
//                 type="text"
//                 value={tempButtonLink}
//                 onChange={(e: ChangeEvent<HTMLInputElement>) =>
//                   setTempButtonLink(e.target.value)
//                 }
//                 className="w-full border rounded px-3 py-2"
//               />
//             </div>
//           </div>
//           <DialogFooter>
//             <Button
//               variant="outline"
//               onClick={() => setIsEntryEditOpen(false)}
//             >
//               Cancel
//             </Button>
//             <Button className='bg-brand text-white hover:bg-brand/80 hover:text-white' onClick={saveEntryEdit}>Save</Button>
//           </DialogFooter>
//         </DialogContent>
//       </Dialog>

//       <div className="bg-white rounded-lg shadow overflow-x-auto">
//         <table className="min-w-full text-left">
//           <thead className="bg-gray-50">
//             <tr>
//               <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">
//                 Title
//               </th>
//               <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">
//                 Subtitle
//               </th>
//               <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">
//                 Button Text
//               </th>
//               <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">
//                 Button Link
//               </th>
//               <th className="px-6 py-3"></th>
//             </tr>
//           </thead>
//           <tbody className="bg-white divide-y divide-gray-200">
//             {entries.map((entry) => (
//               <tr key={entry.id}>
//                 <td className="px-6 py-4 text-sm text-gray-700">
//                   {entry.title}
//                 </td>
//                 <td className="px-6 py-4 text-sm text-gray-700">
//                   {entry.subtitle}
//                 </td>
//                 <td className="px-6 py-4 text-sm text-gray-700">
//                   {entry.buttonText}
//                 </td>
//                 <td className="px-6 py-4 text-sm text-blue-600 hover:underline">
//                   {entry.buttonLink}
//                 </td>
//                 <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
//                   <Button
//                     variant="ghost"
//                     size="sm"
//                     className='bg-brand text-white hover:bg-brand/80 hover:text-white'
//                     onClick={() => openEntryEdit(entry)}
//                   >
//                     Edit
//                   </Button>
//                   <Button
//                     variant="destructive"
//                     size="sm"
//                     onClick={() => handleDelete(entry.id)}
//                   >
//                     Delete
//                   </Button>
//                 </td>
//               </tr>
//             ))}
//           </tbody>
//         </table>
//       </div>
//     </div>
//   );
// }
