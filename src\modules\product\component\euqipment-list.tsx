import { Button } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { IPackageEquipment } from "@/types/package_"
import { Edit, Trash2 } from 'lucide-react'

export function EquipmentList({
  items,
  onEdit,
  onDelete,
}: {
  items: IPackageEquipment[],
  onEdit: (id: string) => void,
  onDelete: (id: string) => void
}) {
  if (items.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Package Equipment List</CardTitle>
        </CardHeader>
        <CardContent className="text-center text-gray-500">
          You don&apos;t have any equipment.
        </CardContent>
      </Card>
    )
  }

  const stripHtml = (html: string) => {
    const tmp = document.createElement('div')
    tmp.innerHTML = html
    return tmp.textContent || tmp.innerText || ''
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Package Equipment List</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Head</TableHead>
                <TableHead>Face</TableHead>
                <TableHead>Body</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map(item => (
                <TableRow key={item.id}>
                  <TableCell>{item.title}</TableCell>
                  <TableCell>{item.description}</TableCell>
                  <TableCell>{stripHtml(item.head)}</TableCell>
                  <TableCell>{stripHtml(item.face)}</TableCell>
                  <TableCell>{stripHtml(item.body)}</TableCell>
                  <TableCell>
                    <Button onClick={() => onEdit(item.id)} size="sm" variant="outline">
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button onClick={() => onDelete(item.id)} size="sm" variant="outline" className="ml-2 text-red-600">
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
