'use client'

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import dynamic from "next/dynamic"
import { IPackageHighlights } from "@/types/package_"

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false })

interface PackageHighlightsFormProps {
  highlights?: Partial<IPackageHighlights> | undefined; 
  onHighlightsChange: (highlights: Partial<IPackageHighlights>) => void;
}

export function PackageHighlightsForm({ highlights, onHighlightsChange }: PackageHighlightsFormProps) {
  const handleChange = (field: keyof IPackageHighlights, value: string) => {
    onHighlightsChange({
      ...highlights,
      [field]: value
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Package Highlights</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="highlights-title">Title</Label>
          <Input
            id="highlights-title"
            value={highlights?.title || ''}
            onChange={(e) => handleChange('title', e.target.value)}
            placeholder="Enter highlights title"
            className="border-black/20 rounded-none"
          />
        </div>
        <div>
          <Label htmlFor="highlights-description">Description</Label>
          <div className="mt-1">
            <RichTextEditor
              value={highlights?.description || ''}
              onChange={(data) => handleChange('description', data)}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
