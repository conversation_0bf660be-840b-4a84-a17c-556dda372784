"use client";

import React, { useEffect, useState } from "react";
import { UseGetTailoredAdventure } from "../tailored-adventure/queries/use-get-tailored-adv";
import { toast } from "sonner";
import Link from "next/link";
import { Pen, Trash } from "lucide-react";
import { useRouter } from "next/navigation";
import { UseUpdateTailoredAdventure } from "../tailored-adventure/mutations/use-update-tailored-adv";

const TailoredAdventureSection: React.FC = () => {
  const { data, isLoading, error } = UseGetTailoredAdventure();
  const updateTailoredAdventure = UseUpdateTailoredAdventure();
  const router = useRouter();

  const [tailoredBlock, setTailoredBlock] = useState(data?.data || null);

  useEffect(() => {
    if (data?.data) {
      setTailoredBlock(data.data);
    }
  }, [data]);

  const handleDeleteFeature = (featureId: string) => {
    if (!tailoredBlock) return;
    const confirmed = window.confirm("Are you sure you want to delete this feature?");
    if (!confirmed) return;

    const updatedFeatures = tailoredBlock.features.filter(feature => feature.id !== featureId);
    const updatedTailoredAdventure = { ...tailoredBlock, features: updatedFeatures };

    updateTailoredAdventure.mutate(updatedTailoredAdventure, {
      onSuccess: () => {
        toast.success("Feature deleted successfully.");
        setTailoredBlock(updatedTailoredAdventure);
      },
      onError: (err) => {
        toast.error(`Delete failed: ${err.message}`);
      }
    });
  };

  if (isLoading) return <p>Loading tailored adventure...</p>;
  if (error) return <p>Error loading tailored adventure: {error.message}</p>;

  if (!tailoredBlock) {
    return (
      <div className="p-6 max-w-full mx-auto">
        <p className="mb-4 text-lg font-medium">No tailored adventure data found.</p>
        <button
          className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition"
          onClick={() => router.push("/home/<USER>/create")}
        >
          Create Tailored Adventure
        </button>
      </div>
    );
  }

  return (
    <div>
      <section className="p-6">
        <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-3xl font-bold">Heading: {tailoredBlock.title}</h1>
            <button
              className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition"
              onClick={() => router.push("/home/<USER>/edit")}
            >
              Edit Tailored Adventure
            </button>
          </div>
          {tailoredBlock.subtitle && (
            <p className="mb-6">Sub Heading: {tailoredBlock.subtitle}</p>
          )}
          <div className="bg-white rounded-lg shadow">
            {tailoredBlock.features.length > 0 ? (
              <table className="min-w-full text-left border-separate border-spacing-0">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="border px-4 py-2">SN</th>
                    <th className="border px-4 py-2">Feature Title</th>
                    <th className="border px-4 py-2">Feature Subtitle</th>
                    <th className="border px-4 py-2">Link Label</th>
                    <th className="border px-4 py-2">Link URL</th>
                    <th className="border px-4 py-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {tailoredBlock.features.map((feature, idx) => (
                    <tr key={feature.id} className="even:bg-gray-50">
                      <td className="border px-4 py-2">{idx + 1}</td>
                      <td className="border px-4 py-2">{feature.title}</td>
                      <td className="border px-4 py-2">{feature.subtitle}</td>
                      <td className="border px-4 py-2">{feature.linkLabel}</td>
                      <td className="border px-4 py-2">
                        <Link
                          href={feature.linkUrl}
                          target="_blank"
                          rel="noreferrer"
                          className="text-blue-600 hover:underline"
                        >
                          {feature.linkUrl}
                        </Link>
                      </td>
                      <td className="py-2 px-4 flex gap-2">
                        <div className="flex space-x-2">
                          <Link
                            href={`/home/<USER>/edit`}
                            className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                          >
                            <Pen className="w-4 h-4" />
                          </Link>
                          <button
                            type="button"
                            onClick={() => handleDeleteFeature(feature.id)}
                            className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                          >
                            <Trash className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <p className="mb-6">No features found.</p>
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default TailoredAdventureSection;
