import { IHikingArea } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function UseDeleteHikingArea() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IHikingArea>, Error, IHikingArea>({
    mutationFn: (data: IHikingArea) =>
      fetch(`/api/home-hiking-areas/${data.id}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['hiking-area'] });
      toast.success('Hiking Area Deleted Sucessfully');
    },
    onError: () => {
      toast.error('Error Deleting Hiking Area');
    },
  });
}
