'use client'

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import dynamic from "next/dynamic"
import { Checkbox } from "@/components/ui/checkbox"
import { IPackage } from "@/types/package_"
import { useState } from "react"
import ImageUploadSingleWithMutation from "@/modules/images/components/upload-single-image"

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false })


interface CreatePackageFormProps {
  formData: Partial<IPackage>;
  onFormDataChange: (data: Partial<IPackage>) => void;
  activities: { id: string; name: string }[];
  regions: { id: string; name: string }[];
  onSuccess?: (pkg: IPackage) => void;
}

const initialFormState: Partial<IPackage> = {
  name: "",
  slug: "",
  regionId: "",
  activityId: "",
  accomodation: "",
  distance: "",
  type: "",
  duration: "",
  altitude: "",
  meals: "",
  groupSize: "",
  price: "",
  discountPrice: "",
  bestSeason: "",
  transport: "",
  activityPerDay: "",
  grade: "",
  bookingLink: "",
  overviewDescription: "",
  thumbnail: "",
  mainImage: "",
  mainImageAlt: "",
  pdfBrochure: "",
  published: false,
  tripOftheMonth: false,
  popularTour: false,
  shortTrek: false,
  itinerary: [],
  equipment: [],
  costDate: [],
  groupPrice: [],
  faq: [],
  review: [],
};

export function PackageDetailsForm({ formData, onFormDataChange, activities, regions, onSuccess }: CreatePackageFormProps) {

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  function handleChange<K extends keyof IPackage>(field: K, value: IPackage[K]) {
    onFormDataChange({ ...formData, [field]: value });
  }

  function handleFileChange(field: keyof IPackage, file: File | null) {
    onFormDataChange({
      ...formData,
      [field]: file || undefined
    });
  }

  // async function handleSubmit(e: React.FormEvent) {
  //   e.preventDefault();
  //   setLoading(true);
  //   setError(null);

  //   try {
  //     const newPackage = await createPackage(formData);
  //     setLoading(false);
  //     if (onSuccess) onSuccess(newPackage);
  //     alert("Package created successfully");
  //     setFormData(initialFormState);
  //   } catch (err: any) {
  //     setLoading(false);
  //     setError(err.message || "Failed to create package");
  //   }
  // }

  return (
    <>
      <form>
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="package-name" className="mb-2">Package Name(H1)</Label>
                  <Input
                    id="package-name"
                    value={formData.name}
                    onChange={(e) => handleChange('name', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="Enter package name"
                    required
                  />
                </div>

                <div>
                  <label className="block font-medium mb-1" htmlFor="regionId">Region</label>
                  <select
                    id="regionId"
                    required
                    value={formData.regionId || ""}
                    onChange={(e) => handleChange("regionId", e.target.value)}
                    className="w-full border p-2 rounded"
                  >
                    <option value="">Select region</option>
                    {regions.map((r) => (
                      <option key={r.id} value={r.id}>
                        {r.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <Label htmlFor="accommodation" className="mb-2">Accommodation</Label>
                  <Input
                    id="accommodation"
                    value={formData.accomodation}
                    onChange={(e) => handleChange('accomodation', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="e.g., Hotel, Lodge and Tea house"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="trail-type" className="mb-2">Trail Type</Label>
                  <Input
                    id="trail-type"
                    value={formData.type}
                    onChange={(e) => handleChange('type', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="e.g., Mountain trail, Forest path"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="max-altitude" className="mb-2">Max Altitude</Label>
                  <Input
                    id="max-altitude"
                    value={formData.altitude}
                    onChange={(e) => handleChange('altitude', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="e.g., 4,600 m | 15091 ft"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="group-size" className="mb-2">Group Size</Label>
                  <Input
                    id="group-size"
                    value={formData.groupSize}
                    onChange={(e) => handleChange('groupSize', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="e.g., 1-25"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="best-season" className="mb-2">Best Season</Label>
                  <Input
                    id="best-season"
                    value={formData.bestSeason}
                    onChange={(e) => handleChange('bestSeason', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="e.g., March-May / August-November"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="duration" className="mb-2">Duration</Label>
                  <Input
                    id="duration"
                    value={formData.duration}
                    onChange={(e) => handleChange('duration', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="e.g., 6 Nights 7 Days"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="price" className="mb-2">Price</Label>
                  <Input
                    id="price"
                    value={formData.price}
                    onChange={(e) => handleChange('price', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="e.g., $ 630"
                    required
                  />
                </div>

                <ImageUploadSingleWithMutation
                  label="Main Image"
                  accept="image/*"
                  initialUrl={formData.mainImage || ''}
                  onUploaded={(url) => handleChange('mainImage', url)}
                />


                <div>
                  <Label htmlFor="image-alt" className="mb-2">Image Alt Tag</Label>
                  <Input
                    id="image-alt"
                    value={formData.mainImageAlt}
                    onChange={(e) => handleChange('mainImageAlt', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="Descriptive alt text for the main image"
                    required
                  />
                </div>

                <ImageUploadSingleWithMutation
                  label="PDF Brochure"
                  accept="application/pdf"
                  initialUrl={formData.pdfBrochure || ''}
                  onUploaded={(url) => handleChange('pdfBrochure', url)}
                />

              </div>

              {/* Right Column */}
              <div className="space-y-4" >
                <div>
                  <label className="block font-medium mb-1" htmlFor="activityId">Activity</label>
                  <select
                    id="activityId"
                    required
                    value={formData.activityId || ""}
                    onChange={(e) => handleChange("activityId", e.target.value)}
                    className="w-full border p-2 rounded"
                  >
                    <option value="">Select activity</option>
                    {activities.map((a) => (
                      <option key={a.id} value={a.id}>
                        {a.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <Label htmlFor="slug" className="mb-2">Slug</Label>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={(e) => handleChange('slug', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="e.g., khopra-trek-nepal"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="distance" className="mb-2">Distance</Label>
                  <Input
                    id="distance"
                    value={formData.distance}
                    onChange={(e) => handleChange('distance', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="e.g., 55 km"
                    required
                  />
                </div>

                {/* <div>
              <Label htmlFor="days-nights">Days And Nights</Label>
              <Input 
                id="days-nights" 
                value={formData.daysNights}
                onChange={(e) => handleChange('daysNights', e.target.value)}
                placeholder="e.g., 6 Nights 7 Days"
              />
            </div> */}

                <div>
                  <Label htmlFor="meals" className="mb-2">Meals Include</Label>
                  <Input
                    id="meals"
                    value={formData.meals}
                    onChange={(e) => handleChange('meals', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="e.g., Breakfast, Lunch & Dinner"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="discount-price" className="mb-2">Discount Price</Label>
                  <Input
                    id="discount-price"
                    value={formData.discountPrice}
                    onChange={(e) => handleChange('discountPrice', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="e.g., $ 525"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="transportation" className="mb-2">Transportation</Label>
                  <Input
                    id="transportation"
                    value={formData.transport}
                    onChange={(e) => handleChange('transport', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="e.g., Jeep / bus"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="booking-link" className="mb-2">Booking Link</Label>
                  <Input
                    id="booking-link"
                    value={formData.bookingLink}
                    onChange={(e) => handleChange('bookingLink', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="WeTravel booking widget code or link"
                    required
                  />
                </div>

                <ImageUploadSingleWithMutation
                  label="Thumbnail"
                  accept="image/*"
                  initialUrl={formData.thumbnail || ''}
                  onUploaded={(url) => handleChange('thumbnail', url)}
                />


                <div>
                  <Label htmlFor="activity-per-day" className="mb-2">Activity Per Day</Label>
                  <Input
                    id="activity-per-day"
                    value={formData.activityPerDay}
                    onChange={(e) => handleChange('activityPerDay', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="e.g., 6-7 hrs"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="grade" className="mb-2">Grade</Label>
                  <Input
                    id="grade"
                    value={formData.grade}
                    onChange={(e) => handleChange('grade', e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="e.g., Moderate, Easy, Challenging"
                    required
                  />
                </div>
              </div>
            </div>

            <div className="mt-6">
              <Label htmlFor="overview" className="mb-2">Overview Description</Label>
              <div className="mt-1">
                <RichTextEditor
                  value={formData.overviewDescription || ''}
                  onChange={(data) => handleChange('overviewDescription', data)}
                />
              </div>
            </div>

            <div className="flex flex-col space-y-3 col-span-2">
              <div className="flex gap-4 flex-wrap mt-8">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="published"
                    checked={formData.published || false}
                    onCheckedChange={(checked) => handleChange('published', !!checked)}
                    className="border-black/20 rounded-none"
                  />
                  <Label htmlFor="published">Published</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="tripOfTheMonth"
                    checked={formData.tripOftheMonth || false}
                    onCheckedChange={(checked) => handleChange('tripOftheMonth', !!checked)}
                    className="border-black/20 rounded-none"
                  />
                  <Label htmlFor="tripOfTheMonth">Trip of the Month</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="popularTour"
                    checked={formData.popularTour || false}
                    onCheckedChange={(checked) => handleChange('popularTour', !!checked)}
                    className="border-black/20 rounded-none"
                  />
                  <Label htmlFor="popularTour">Popular Tour</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="shortTrek"
                    checked={formData.shortTrek || false}
                    onCheckedChange={(checked) => handleChange('shortTrek', !!checked)}
                    className="border-black/20 rounded-none"
                  />
                  <Label htmlFor="shortTrek">Short Trek</Label>
                </div>
              </div>
            </div>

          </CardContent>
        </Card>
      </form>
    </>
  )
}


// 'use client'

// import { Input } from "@/components/ui/input"
// import { Label } from "@/components/ui/label"
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
// import { Card, CardContent } from "@/components/ui/card"
// import { FileUpload } from "./fileupload"
// import dynamic from "next/dynamic"
// import { useState } from "react"

// const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false })

// export function PackageDetailsForm() {
//   const [overview, setOverview] = useState("")
//   return (
//     <Card>
//       <CardContent className="pt-6">
//         <div className="grid grid-cols-2 gap-6">
//           {/* Left Column */}
//           <div className="space-y-4">
//             <div>
//               <Label htmlFor="package-name">Package Name(H1)</Label>
//               <Input id="package-name" defaultValue="Khopra Ridge Trek" />
//             </div>
//             <div>
//               <Label htmlFor="region">Select Region</Label>
//               <Select defaultValue="annapurna">
//                 <SelectTrigger>
//                   <SelectValue />
//                 </SelectTrigger>
//                 <SelectContent>
//                   <SelectItem value="annapurna">Annapurna Region</SelectItem>
//                   <SelectItem value="everest">Everest Region</SelectItem>
//                   <SelectItem value="langtang">Langtang Region</SelectItem>
//                 </SelectContent>
//               </Select>
//             </div>
//             <div>
//               <Label htmlFor="accommodation">Accommodation</Label>
//               <Input id="accommodation" defaultValue="Hotel, Lodge and Tea house" />
//             </div>
//             <div>
//               <Label htmlFor="trail-type">Trail Type</Label>
//               <Input id="trail-type" defaultValue="" />
//             </div>
//             <div>
//               <Label htmlFor="max-altitude">Max Altitude</Label>
//               <Input id="max-altitude" defaultValue="4,600 m | 15091 ft" />
//             </div>
//             <div>
//               <Label htmlFor="group-size">Group Size</Label>
//               <Input id="group-size" defaultValue="1-25" />
//             </div>
//             <div>
//               <Label htmlFor="best-season">Best Season</Label>
//               <Input id="best-season" defaultValue="March-May / August-November" />
//             </div>
//             <div>
//               <Label htmlFor="price">Price</Label>
//               <Input id="price" defaultValue="$ 630" />
//             </div>
//             <div>
//               <Label htmlFor="activity-per-day">Activity Per Day</Label>
//               <Input id="activity-per-day" defaultValue="6-7 hrs" />
//             </div>
//             <div>
//               <Label htmlFor="grade">Grade</Label>
//               <Input id="grade" defaultValue="Moderate" />
//             </div>
//             <div>
//               <FileUpload
//                 label="Image"
//                 accept="image/*"
//                 showPreview={true}
//                 previewAlt="Package preview" onFileChange={function (): void {
//                   throw new Error("Function not implemented.")
//                 } }              />
//             </div>
//             <div>
//               <FileUpload
//                 label="PDF"
//                 accept=".pdf" onFileChange={function (): void {
//                   throw new Error("Function not implemented.")
//                 } }              />
//             </div>
//           </div>

//           {/* Right Column */}
//           <div className="space-y-4">
//             <div>
//               <Label htmlFor="activity">Select Activity</Label>
//               <Select defaultValue="trekking">
//                 <SelectTrigger>
//                   <SelectValue />
//                 </SelectTrigger>
//                 <SelectContent>
//                   <SelectItem value="trekking">Trekking</SelectItem>
//                   <SelectItem value="peak-climbing">Peak Climbing</SelectItem>
//                   <SelectItem value="trail-running">Trail Running</SelectItem>
//                   <SelectItem value="fastpacking">Fast Packing</SelectItem>
//                 </SelectContent>
//               </Select>
//             </div>
//             <div>
//               <Label htmlFor="slug">Slug</Label>
//               <Input id="slug" defaultValue="khopra-trek-nepal" />
//             </div>
//             <div>
//               <Label htmlFor="distance">Distance</Label>
//               <Input id="distance" defaultValue="55 km" />
//             </div>
//             <div>
//               <Label htmlFor="days-nights">Days And Nights (eg. 3 Nights 4 Days)</Label>
//               <Input id="days-nights" defaultValue="6 Nights 7 Days" />
//             </div>
//             <div>
//               <Label htmlFor="meals">Meals Include</Label>
//               <Input id="meals" defaultValue="Breakfast, Lunch & Dinner" />
//             </div>
//             <div>
//               <Label htmlFor="discount-price">Discount Price</Label>
//               <Input id="discount-price" defaultValue="$ 525" />
//             </div>
//             <div>
//               <Label htmlFor="transportation">Transportation</Label>
//               <Input id="transportation" defaultValue="Jeep / bus" />
//             </div>
//             <div>
//               <Label htmlFor="image-alt">Image Alt Tag</Label>
//               <Input id="image-alt" defaultValue="trekkers enjoying the view in Khopra ridge and Mt. Dhaulagiri as seen in background" />
//             </div>
//             <div>
//               <FileUpload
//                 label="Thumbnail"
//                 accept="image/*"
//                 showPreview={true}
//                 previewSrc="/placeholder.svg?height=80&width=120"
//                 previewAlt="Thumbnail preview" onFileChange={function (): void {
//                   throw new Error("Function not implemented.")
//                 } }              />
//             </div>
//             <div>
//               <Label htmlFor="booking-link">Booking link</Label>
//               <Input id="booking-link" defaultValue='<button class="wtrvl-checkout_button" id="wetravel_button_widget" data-env="https://ww' />
//             </div>
//           </div>
//         </div>

//         <div className="mt-6">
//           <Label htmlFor="overview">Overview Description</Label>
//           <div className="mt-1">
//             <RichTextEditor value={overview} onChange={data => setOverview(data)} />

//           </div>
//         </div>
//       </CardContent>
//     </Card>
//   )
// }
