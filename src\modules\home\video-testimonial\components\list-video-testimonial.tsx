"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { UseGetTestimonial } from "../queries/use-get-testimonial";
import Image from "next/image";

const ListVideoTestimonials: React.FC = () => {
  const { data, isLoading, error } = UseGetTestimonial();
  const router = useRouter();

  if (isLoading) return <p>Loading video testimonials...</p>;
  if (error) return <p>Error loading video testimonials: {error.message}</p>;

  const testimonialBlock = data?.data;

  return (
    <div className="p-6 container mx-auto">
      {testimonialBlock ? (
        <>
          <h1 className="text-3xl font-bold mb-4">{testimonialBlock.title}</h1>
          <p className="mb-6">{testimonialBlock.subtitle}</p>

          {testimonialBlock.testimonials.length > 0 ? (
            <table className="min-w-full border border-gray-300 mb-6">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border px-4 py-2">Thumbnail</th>
                  <th className="border px-4 py-2">Title</th>
                  <th className="border px-4 py-2">Destination</th>
                  <th className="border px-4 py-2">YouTube URL</th>
                  <th className="border px-4 py-2">Date</th>
                </tr>
              </thead>
              <tbody>
                {testimonialBlock.testimonials.map((video) => (
                  <tr key={video.id} className="even:bg-gray-50">
                    <td className="border px-4 py-2">
                      {video.youtubeThumbnail ? (
                        <Image
                          src={video.youtubeThumbnail}
                          alt={video.title}
                          width={100}
                          height={60}
                          className="w-24 h-14 object-cover rounded"
                        />
                      ) : (
                        <div className="w-24 h-14 bg-gray-200 rounded flex items-center justify-center text-gray-400 text-sm">
                          No Thumbnail
                        </div>
                      )}
                    </td>
                    <td className="border px-4 py-2">{video.title}</td>
                    <td className="border px-4 py-2">{video.destination}</td>
                    <td className="border px-4 py-2">
                      <a
                        href={video.youtubeUrl}
                        target="_blank"
                        rel="noreferrer"
                        className="text-blue-600 hover:underline"
                      >
                        Link
                      </a>
                    </td>
                    <td className="border px-4 py-2">{new Date(video.date).toLocaleDateString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <p className="mb-6">No video testimonials found.</p>
          )}

          <div className="flex space-x-4 justify-center">
            {/* <button
              className="bg-brand text-white px-4 py-2 rounded hover:bg-brand/80"
              onClick={() => router.push("/home/<USER>/create")}
            >
              Create Video Testimonial
            </button> */}
            <button
              className="bg-brand text-white px-4 py-2 rounded hover:bg-brand/80"
              onClick={() => router.push("/home/<USER>/edit")}
            >
              Edit Video Testimonials
            </button>
          </div>
        </>
      ) : (
        <p>No testimonial data found. Please create one.</p>
      )}
    </div>
  );
};

export default ListVideoTestimonials;
