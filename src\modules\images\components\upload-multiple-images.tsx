"use client";

import React, { useRef, useState } from "react";
import { useUploadMultipleImages } from "../mutations/use-upload-multiple-images";
import Image from "next/image";

interface ImageUploadMultipleWithMutationProps {
  onUploaded: (urls: string[]) => void;
  label?: string;
  accept?: string;
}

const ImageUploadMultipleWithMutation: React.FC<ImageUploadMultipleWithMutationProps> = ({
  onUploaded,
  label = "Upload Images",
  accept = "image/*",
}) => {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const uploadMutation = useUploadMultipleImages();

  const handleClick = () => {
    inputRef.current?.click();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const filesArray = Array.from(files);
    setPreviewUrls(filesArray.map((file) => URL.createObjectURL(file)));

    uploadMutation.mutate(filesArray, {
      onSuccess: (result) => {
        if (result.success && result.data.length) {
          const urls = result.data.map((d) => d.url);
          setPreviewUrls(urls);
          onUploaded(urls);
        } else {
          alert("Upload failed");
          setPreviewUrls([]);
        }
      },
      onError: () => {
        alert("Error uploading images");
        setPreviewUrls([]);
      },
    });
  };

  return (
    <div className="flex flex-col items-center gap-2">
      {label && <p className="mb-2 font-medium">{label}</p>}

      <div className="flex gap-2 flex-wrap">
        {previewUrls.map((url, idx) => (
          <Image
            key={idx}
            src={url}
            width={100}
            height={100}
            alt={`preview-${idx}`}
            className="w-24 h-24 object-cover rounded border"
          />
        ))}
      </div>

      <input
        ref={inputRef}
        type="file"
        accept={accept}
        multiple
        onChange={handleChange}
        className="hidden"
      />
      <button
        type="button"
        onClick={handleClick}
        className="bg-brand text-white px-3 py-1 rounded hover:bg-brand/80"
      >
        Upload
      </button>
    </div>
  );
};

export default ImageUploadMultipleWithMutation;
