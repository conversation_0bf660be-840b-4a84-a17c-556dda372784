import React from 'react'
import { UseGetHero } from '../hero/queries/use-get-home';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { Pen, Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { UseDeleteHero } from '../hero/mutations/use-delete-hero';
import { toast } from 'sonner';

const HeroSection = () => {

    const { data, isLoading, error } = UseGetHero();
    const deleteHeroMutation = UseDeleteHero();
    const router = useRouter();

    if (isLoading) return <p>Loading hero...</p>;
    if (error) return <p>Error loading hero: {error.message}</p>;

    const hero = data?.data;

    const handleDelete = () => {
        if (!hero) return;

        const confirmed = window.confirm("Are you sure you want to delete the hero?");
        if (!confirmed) return;

        deleteHeroMutation.mutate(hero, {
            onSuccess: () => {
                toast.success("Hero deleted successfully");
            },
            onError: (err) => {
                toast.error(`Delete failed: ${err.message}`);
            }
        });
    };


    return (
        <div>
            <section className="p-6">
                <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
                    {hero ? (
                        <div>
                            <div className="flex items-center justify-between mb-6">
                                <h1 className="text-3xl font-bold">Hero Section</h1>
                                <Button
                                    className="bg-black text-white px-3 py-0 rounded hover:bg-gray-900 transition"
                                    onClick={() => router.push("/home/<USER>/edit")}
                                >
                                    Edit
                                </Button>
                            </div>
                            <div className="bg-white rounded-lg shadow">
                                <table className="min-w-full text-left border-separate border-spacing-0">
                                    <thead>
                                        <tr className='bg-gray-100'>
                                            <th className="border px-4 py-2">SN</th>
                                            <th className="border py-3 px-4">Titles</th>
                                            <th className="border py-3 px-4 ">Subtitles</th>
                                            <th className="border py-3 px-4 ">Images</th>
                                            <th className="border py-3 px-4 ">Video</th>
                                            <th className="border py-3 px-4 ">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr className="border-t last:border-b hover:bg-gray-100 transition">
                                            <td className="border py-2 px-4">1</td>
                                            <td className="border py-2 px-4">
                                                {hero?.titles && hero.titles.length > 0
                                                    ? hero.titles.map((title, i) => (
                                                        <div key={i}>{title}</div>
                                                    ))
                                                    : "-"}
                                            </td>
                                            <td className="border py-2 px-4">
                                                {hero?.subtitles && hero.subtitles.length > 0
                                                    ? hero.subtitles.map((subtitle, i) => (
                                                        <div key={i}>{subtitle}</div>
                                                    ))
                                                    : "-"}
                                            </td>
                                            <td className="border py-2 px-4">
                                                {hero?.images && hero.images.length > 0 ? (
                                                    <div className="flex gap-2">
                                                        {hero.images.map((img, i) => (
                                                            <Image
                                                                key={i}
                                                                src={img}
                                                                alt={`Hero image ${i}`}
                                                                width={60}
                                                                height={40}
                                                                className="rounded object-cover"
                                                                style={{ width: '60px', height: '40px' }}
                                                            />
                                                        ))}
                                                    </div>
                                                ) : (
                                                    "-"
                                                )}
                                            </td>
                                            <td className="border py-2 px-4">
                                                {hero?.videoUrl ? (
                                                    <video
                                                        src={hero.videoUrl}
                                                        controls
                                                        width={80}
                                                        height={48}
                                                        className="rounded"
                                                        style={{ objectFit: "cover" }}
                                                        preload="metadata"
                                                    />
                                                ) : (
                                                    "-"
                                                )}
                                            </td>
                                            <td className="py-2 px-4 flex gap-2">
                                                <div className="flex space-x-2">
                                                    <Link
                                                        href={`/home/<USER>/edit`}
                                                        className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                                                    >
                                                        <Pen className="w-4 h-4" />
                                                    </Link>
                                                    <button
                                                        onClick={handleDelete}
                                                        className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                                                        type="button"
                                                    >
                                                        <Trash className="w-4 h-4" />
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    ) : (
                        <>
                            <p className="mb-6">No hero section found.</p>
                            <button
                                className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition"
                                onClick={() => router.push("/home/<USER>/create")}
                            >
                                Create Hero Section
                            </button>
                        </>
                    )}
                </div>
            </section>
        </div>
    )
}

export default HeroSection