"use client";

import React, { useState } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useCreateActivity } from "../mutation/use-create-activity";
import ImageUploadSingleWithMutation from "@/modules/images/components/upload-single-image";

const CreateActivity: React.FC = () => {
    const [name, setName] = useState("");
    const [slug, setSlug] = useState("");
    const [description, setDescription] = useState("");
    const [image, setImage] = useState("");

    const createActivity = useCreateActivity();
    const router = useRouter();

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!name || !slug || !description || !image) {
            toast.error("All fields are required");
            return;
        }

        createActivity.mutateAsync(
            { name, slug, description, image } as any,
            {
                onSuccess: () => {
                    toast.success("Activity created successfully!");
                    router.push("/activities-offer");
                },
                onError: (err: Error) => {
                    toast.error(`Failed to create activity: ${err.message}`);
                },
            }
        );
    };

    return (
        <div className="p-6 container mx-auto bg-white rounded shadow">
            <h1 className="text-2xl font-bold mb-6">Create New Activity</h1>
            <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                    <label htmlFor="name" className="block mb-1 font-medium">
                        Name
                    </label>
                    <input
                        id="name"
                        type="text"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="w-full border rounded px-3 py-2"
                        required
                    />
                </div>

                <div>
                    <label htmlFor="slug" className="block mb-1 font-medium">
                        Slug
                    </label>
                    <input
                        id="slug"
                        type="text"
                        value={slug}
                        onChange={(e) => setSlug(e.target.value)}
                        className="w-full border rounded px-3 py-2"
                        required
                    />
                </div>

                <div>
                    <label className="block mb-1 font-medium">Image</label>
                    <ImageUploadSingleWithMutation
                        initialUrl={image}
                        onUploaded={(url) => setImage(url)}
                    />
                </div>


                <div>
                    <label htmlFor="description" className="block mb-1 font-medium">
                        Description
                    </label>
                    <textarea
                        id="description"
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        className="w-full border rounded px-3 py-2"
                        rows={4}
                        required
                    ></textarea>
                </div>

                <button
                    type="submit"
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
                >
                    Create
                </button>
            </form>
        </div>
    );
};

export default CreateActivity;
