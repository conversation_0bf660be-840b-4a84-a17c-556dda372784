"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

interface Props {
    schema: string
    setSchema: (schema: string) => void;
}

export function SchemaDetailsSection({ schema, setSchema }: Props) {
    return (
        <Card>
            <CardHeader>
                <CardTitle>Schema Details</CardTitle>
            </CardHeader>
            <CardContent>
                <Label htmlFor="schema" className="mb-2">Schema</Label>
                <Textarea
                    id="schema"
                    value={schema}
                    onChange={e => setSchema(e.target.value)}
                    className="border-black/20 rounded-none"
                    placeholder="Paste JSON-LD here"
                    rows={10}
                />
            </CardContent>
        </Card>
    )
}
