import { useState, useEffect, FormEvent } from "react"
import { Plus, Edit, X } from 'lucide-react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { IPackageCostDate } from "@/types/package_"

const initialFormState = {
  id: "",
  packageId: "",
  days: "",
  startDate: "",
  endDate: "",
  price: "",
  discountPrice: "",
  tripStatus: "",
  published: false,
  markUpcomingTreks: false,
}

export function AddCostDateForm({
  editingItem,
  onAdd,
  onUpdate,
  onCancelEdit
}: {
  editingItem: IPackageCostDate | null,
  onAdd: (data: Omit<IPackageCostDate, 'id'>) => void,
  onUpdate: (data: IPackageCostDate) => void,
  onCancelEdit: () => void,
}) {
  const [formData, setFormData] = useState(initialFormState)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const isEditing = editingItem !== null

  useEffect(() => {
    if (isEditing && editingItem) {
      setFormData({
        id: editingItem.id,
        packageId: editingItem.packageId,
        days: editingItem.days,
        startDate: editingItem.startDate,
        endDate: editingItem.endDate,
        price: editingItem.price,
        discountPrice: editingItem.discountPrice,
        tripStatus: editingItem.tripStatus,
        published: editingItem.published,
        markUpcomingTreks: editingItem.markUpcomingTreks,
      })
    } else {
      setFormData(initialFormState)
    }
  }, [editingItem, isEditing])

  const handleInput = (field: keyof typeof initialFormState, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    try {
      if (isEditing && editingItem) {
        const { id, ...editableFields } = formData
        onUpdate({
          ...editingItem,  
          ...editableFields 
        })
      } else {
        const { id, ...newItemData } = formData
        onAdd(newItemData)
      }
      setFormData(initialFormState)
    } finally {
      setIsSubmitting(false)
    }
  }
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isEditing ? <Edit className="w-5 h-5" /> : <Plus className="w-5 h-5" />}
          {isEditing ? 'Edit Cost & Date' : 'Add Cost & Date'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="days" className="mb-2">Days</Label>
            <Input id="days" value={formData.days} required className="border-black/20 rounded-none" onChange={e => handleInput('days', e.target.value)} />
          </div>
          <div>
            <Label htmlFor="startDate" className="mb-2">Start Date</Label>
            <Input type="date" id="startDate" value={formData.startDate} required className="border-black/20 rounded-none" onChange={e => handleInput('startDate', e.target.value)} />
          </div>
          <div>
            <Label htmlFor="endDate" className="mb-2">End Date</Label>
            <Input type="date" id="endDate" value={formData.endDate} required className="border-black/20 rounded-none" onChange={e => handleInput('endDate', e.target.value)} />
          </div>
          <div>
            <Label htmlFor="price" className="mb-2">Price</Label>
            <Input id="price" value={formData.price} required className="border-black/20 rounded-none" onChange={e => handleInput('price', e.target.value)} />
          </div>
          <div>
            <Label htmlFor="discountPrice" className="mb-2">Discount Price</Label>
            <Input id="discountPrice" value={formData.discountPrice} required className="border-black/20 rounded-none" onChange={e => handleInput('discountPrice', e.target.value)} />
          </div>
          <div>
            <Label htmlFor="tripStatus" className="mb-2">Trip Status</Label>
            <Input id="tripStatus" value={formData.tripStatus} required className="border-black/20 rounded-none" onChange={e => handleInput('tripStatus', e.target.value)} />
          </div>
          <div>
            <label>
              <input
                type="checkbox"
                checked={formData.published}
                onChange={(e) => handleInput('published', e.target.checked)}
                className="border-black/20 rounded-none"
              /> Publish
            </label>
            <label className="ml-4">
              <input
                type="checkbox"
                checked={formData.markUpcomingTreks}
                onChange={(e) => handleInput('markUpcomingTreks', e.target.checked)}
                className="border-black/20 rounded-none"
              /> Mark Upcoming Treks
            </label>
          </div>
          <div className="flex justify-end gap-2 pt-4 border-t">
            {isEditing && (
              <Button type="button" variant="outline" onClick={onCancelEdit} disabled={isSubmitting}>
                <X className="w-4 h-4" /> Cancel
              </Button>
            )}
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : isEditing ? 'Save Changes' : 'Add Cost & Date'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
