'use client';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { IPackageYtVideo } from '@/types/package_';
import { Trash2, Plus } from 'lucide-react';

interface PackageVideoFormProps {
  ytVideo?: Partial<IPackageYtVideo> | undefined;
  onYtVideoChange: (ytVideo: Partial<IPackageYtVideo>) => void;
}

export function PackageVideoForm({
  ytVideo,
  onYtVideoChange,
}: PackageVideoFormProps) {
  const handleTitleChange = (title: string) => {
    onYtVideoChange({
      ...ytVideo,
      title,
    });
  };

  const handleLinkChange = (index: number, value: string) => {
    const links = [...(ytVideo?.links || [])];
    links[index] = value;
    onYtVideoChange({
      ...ytVideo,
      links,
    });
  };

  const addVideoLink = () => {
    const links = [...(ytVideo?.links || []), ''];
    onYtVideoChange({
      ...ytVideo,
      links,
    });
  };

  const removeVideoLink = (index: number) => {
    const links = (ytVideo?.links || []).filter((_, i) => i !== index);
    onYtVideoChange({
      ...ytVideo,
      links,
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>YouTube Videos</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="video-title">Video Section Title</Label>
          <Input
            id="video-title"
            value={ytVideo?.title || ''}
            onChange={(e) => handleTitleChange(e.target.value)}
            placeholder="Enter video section title"
            className="border-black/20 rounded-none"
          />
        </div>

        <div>
          <div className="flex justify-between items-center mb-2">
            <Label>YouTube Video Links</Label>
            <Button
              type="button"
              onClick={addVideoLink}
              size="sm"
              variant="outline"
              className="border-black/20 rounded-none"
            >
              <Plus className="w-4 h-4 mr-1" />
              Add Video Link
            </Button>
          </div>

          <div className="space-y-2">
            {(ytVideo?.links || []).map((link, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  value={link}
                  onChange={(e) => handleLinkChange(index, e.target.value)}
                  placeholder="https://youtube.com/watch?v=..."
                  className="border-black/20 rounded-none flex-1"
                />
                <Button
                  type="button"
                  onClick={() => removeVideoLink(index)}
                  size="sm"
                  variant="outline"
                  className="border-red-300 text-red-600 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            ))}
          </div>

          {(ytVideo?.links || []).length === 0 && (
            <div className="text-center py-4 text-gray-500">
              No video links added yet. Click &quot;Add Video Link&quot; to
              start.
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
