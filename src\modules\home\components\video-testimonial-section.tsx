"use client";

import React, { useEffect, useState } from "react";
import { UseGetTestimonial } from "../video-testimonial/queries/use-get-testimonial";
import { UseUpdateTestimonial } from "../video-testimonial/mutations/use-update-testimonial";
import { toast } from "sonner";
import Image from "next/image";
import Link from "next/link";
import { Pen, Trash } from "lucide-react";
import { useRouter } from "next/navigation";

const VideoTestimonialSection: React.FC = () => {
  const { data, isLoading, error } = UseGetTestimonial();
  const updateTestimonial = UseUpdateTestimonial();
  const router = useRouter();

  const [testimonialBlock, setTestimonialBlock] = useState(data?.data || null);

  useEffect(() => {
    if (data?.data) {
      setTestimonialBlock(data.data);
    }
  }, [data]);

  const handleDeleteVideo = (videoId: string) => {
    if (!testimonialBlock) return;
    const confirmed = window.confirm("Are you sure you want to delete this testimonial?");
    if (!confirmed) return;

    const updatedVideos = testimonialBlock.testimonials.filter((video) => video.id !== videoId);
    const updatedTestimonialBlock = { ...testimonialBlock, testimonials: updatedVideos };

    updateTestimonial.mutate(updatedTestimonialBlock, {
      onSuccess: () => {
        toast.success("Video testimonial deleted successfully.");
        setTestimonialBlock(updatedTestimonialBlock);
      },
      onError: (err) => {
        toast.error(`Delete failed: ${err.message}`);
      }
    });
  };

  if (isLoading) return <p>Loading video testimonials...</p>;
  if (error) return <p>Error loading video testimonials: {error.message}</p>;

  if (!testimonialBlock) {
    return (
      <div className="p-6 max-w-full mx-auto">
        <p className="mb-4 text-lg font-medium">No testimonial data found. Please create one.</p>
        <button
          className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition"
          onClick={() => router.push("/home/<USER>/create")}
        >
          Create Video Testimonial
        </button>
      </div>
    );
  }

  return (
    <div>
      <section className="p-3">
        <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-3xl font-bold">Heading: {testimonialBlock.title}</h1>
            <button
              className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition"
              onClick={() => router.push("/home/<USER>/edit")}
            >
              Edit Video Testimonials
            </button>
          </div>

          {testimonialBlock.subtitle && (
            <p className="mb-6">Sub Heading: {testimonialBlock.subtitle}</p>
          )}

          <div className="bg-white rounded-lg shadow">
            {testimonialBlock.testimonials.length > 0 ? (
              <table className="min-w-full text-left border-separate border-spacing-0">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="border px-4 py-2">SN</th>
                    <th className="border px-4 py-2">Thumbnail</th>
                    <th className="border px-4 py-2">Title</th>
                    <th className="border px-4 py-2">Destination</th>
                    <th className="border px-4 py-2">YouTube URL</th>
                    <th className="border px-4 py-2">Date</th>
                    <th className="border px-4 py-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {testimonialBlock.testimonials.map((video, idx) => (
                    <tr key={video.id} className="even:bg-gray-50">
                      <td className="border px-4 py-2">{idx + 1}</td>
                      <td className="border px-4 py-2">
                        {video.youtubeThumbnail ? (
                          <Image
                            src={video.youtubeThumbnail}
                            alt={video.title}
                            width={100}
                            height={60}
                            className="w-24 h-14 object-cover rounded"
                          />
                        ) : (
                          <div className="w-24 h-14 bg-gray-200 rounded flex items-center justify-center text-gray-400 text-sm">
                            No Thumbnail
                          </div>
                        )}
                      </td>
                      <td className="border px-4 py-2">{video.title}</td>
                      <td className="border px-4 py-2">{video.destination}</td>
                      <td className="border px-4 py-2">
                        <Link
                          href={video.youtubeUrl}
                          target="_blank"
                          rel="noreferrer"
                          className="text-blue-600 hover:underline"
                        >
                          Link
                        </Link>
                      </td>
                      <td className="border px-4 py-2">{new Date(video.date).toLocaleDateString()}</td>
                      <td className="py-2 px-4 flex gap-2">
                        <div className="flex space-x-2">
                          <Link
                            href={`/home/<USER>/edit`}
                            className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                          >
                            <Pen className="w-4 h-4" />
                          </Link>
                          <button
                            type="button"
                            onClick={() => handleDeleteVideo(video.id)}
                            className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                          >
                            <Trash className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <p className="mb-6">No video testimonials found.</p>
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default VideoTestimonialSection;
