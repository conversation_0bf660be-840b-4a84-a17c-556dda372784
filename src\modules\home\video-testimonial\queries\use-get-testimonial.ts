import { ITestimonial } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function UseGetTestimonial() {
  return useQuery<IApiResponse<ITestimonial>, Error>({
    queryKey: ['testimonial'],
    queryFn: () =>
      fetch(`/api/home-video-testimonials`, {
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((res) => res.json()),
  });
}
