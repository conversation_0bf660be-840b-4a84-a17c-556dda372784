"use client";

import React, { useState, useEffect, ChangeEvent } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import { IAdventure } from '@/types/home';
import { UseGetAdventure } from '../queries/use-get-adventure';
import { UseUpdateAdventure } from '../mutations/use-update-advanture';

const EditDiscoverPage: React.FC = () => {
    const router = useRouter();
    const { id } = useParams();
    const entryId = Array.isArray(id) ? id[0] || '' : id || '';

    const [title, setTitle] = useState<string>('');
    const [images, setImages] = useState<string[]>([]);
    const [points, setPoints] = useState<string[]>(['', '', '', '']);
    const [linkUrl, setLinkUrl] = useState<string>('');
    const [linkLabel, setLinkLabel] = useState<string>('');

    const { data: adventureResponse, isLoading: isFetching, error } = UseGetAdventure();
    const updateAdventureMutation = UseUpdateAdventure();

    const adventures: IAdventure[] = adventureResponse?.data || [];
    const currentAdventure = adventures.find(adventure => adventure.id === entryId);

    useEffect(() => {
        if (currentAdventure) {
            setTitle(currentAdventure.title || '');
            setImages(currentAdventure.images || []);
            setPoints(currentAdventure.points && currentAdventure.points.length > 0
                ? [...currentAdventure.points, ...Array(Math.max(0, 4 - currentAdventure.points.length)).fill('')]
                : ['', '', '', '']
            );
            setLinkUrl(currentAdventure.linkUrl || '');
            setLinkLabel(currentAdventure.linkLabel || '');
        }
    }, [currentAdventure]);

    const handleImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
        if (!e.target.files) return;
        const urls = Array.from(e.target.files).map(file => URL.createObjectURL(file));
        setImages(prev => [...prev, ...urls]);
    };

    const handleRemoveImage = (idx: number) => {
        setImages(prev => prev.filter((_, i) => i !== idx));
    };

    const handlePointChange = (idx: number, value: string) => {
        setPoints(prev => prev.map((pt, i) => i === idx ? value : pt));
    };

    const handleSave = async () => {
        // Basic validation
        if (!title.trim()) {
            alert('Please enter a title');
            return;
        }

        if (!currentAdventure) {
            alert('Adventure data not found');
            return;
        }

        const adventureData: IAdventure = {
            id: entryId,
            title: title.trim(),
            images: images,
            points: points.filter(point => point.trim()),
            linkUrl: linkUrl.trim(),
            linkLabel: linkLabel.trim(),
        };

        try {
            await updateAdventureMutation.mutateAsync(adventureData);
            router.push('/home/<USER>');
        } catch (error) {
            console.error('Failed to update adventure:', error);
        }
    };

    const isSaving = updateAdventureMutation.isPending;
    const hasValidTitle = title.trim().length > 0;

    if (isFetching) {
        return (
            <div className="p-6 bg-gray-50 min-h-screen">
                <div className="flex items-center justify-center h-64">
                    <div className="text-lg text-gray-600">Loading adventure data...</div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="p-6 bg-gray-50 min-h-screen">
                <div className="flex items-center justify-center h-64">
                    <div className="text-center">
                        <div className="text-lg text-red-600 mb-4">Error loading adventure data</div>
                        <Link href="/home/<USER>">
                            <button className="px-4 py-2 bg-brand text-white rounded hover:bg-brand/80">
                                Back to Adventures
                            </button>
                        </Link>
                    </div>
                </div>
            </div>
        );
    }

    if (!currentAdventure) {
        return (
            <div className="p-6 bg-gray-50 min-h-screen">
                <div className="flex items-center justify-center h-64">
                    <div className="text-center">
                        <div className="text-lg text-gray-600 mb-4">Adventure not found</div>
                        <Link href="/home/<USER>">
                            <button className="px-4 py-2 bg-brand text-white rounded hover:bg-brand/80">
                                Back to Adventures
                            </button>
                        </Link>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            <h2 className="text-2xl font-semibold mb-4">Edit Adventure: {currentAdventure.title}</h2>

            {/* Title */}
            <div className="mb-4">
                <label className="block font-medium mb-1">Title *</label>
                <input
                    type="text"
                    value={title}
                    onChange={e => setTitle(e.target.value)}
                    className="w-full border rounded px-3 py-2"
                    placeholder="Entry title"
                    disabled={isSaving}
                    required
                />
            </div>

            {/* Images Upload */}
            <div className="mb-4">
                <label className="block font-medium mb-1">Images</label>
                <input
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={handleImageUpload}
                    disabled={isSaving}
                />
                <div className="mt-2 grid grid-cols-3 gap-3">
                    {images
                        .filter(src => {
                            return typeof src === 'string' &&
                                (src.startsWith('/') || src.startsWith('http://') || src.startsWith('https://'));
                        })
                        .map((src, idx) => (
                            <div key={idx} className="relative">
                                <Image
                                    src={src}
                                    alt={`img-${idx}`}
                                    width={80}
                                    height={80}
                                    className="object-cover rounded"
                                />
                                <button
                                    onClick={() => handleRemoveImage(idx)}
                                    className="absolute top-1 right-1 text-white bg-black bg-opacity-50 rounded-full p-1"
                                    disabled={isSaving}
                                >
                                    ✕
                                </button>
                            </div>
                        ))}

                </div>
            </div>

            {/* Points */}
            <div className="mb-4">
                <label className="block font-medium mb-1">Points (4)</label>
                {points.map((pt, idx) => (
                    <input
                        key={idx}
                        type="text"
                        value={pt}
                        onChange={e => handlePointChange(idx, e.target.value)}
                        className="w-full border rounded px-3 py-2 mb-2"
                        placeholder={`Point #${idx + 1}`}
                        disabled={isSaving}
                    />
                ))}
            </div>

            {/* Link Fields */}
            <div className="mb-6">
                <label className="block font-medium mb-1">Link URL</label>
                <input
                    type="text"
                    value={linkUrl}
                    onChange={e => setLinkUrl(e.target.value)}
                    className="w-full border rounded px-3 py-2 mb-2"
                    placeholder="Enter URL"
                    disabled={isSaving}
                />
                <label className="block font-medium mb-1">Link Label</label>
                <input
                    type="text"
                    value={linkLabel}
                    onChange={e => setLinkLabel(e.target.value)}
                    className="w-full border rounded px-3 py-2"
                    placeholder="Enter link text"
                    disabled={isSaving}
                />
            </div>

            {/* Actions */}
            <div className="flex space-x-2">
                <button
                    onClick={handleSave}
                    disabled={isSaving || !hasValidTitle}
                    className={`px-4 py-2 rounded text-white ${isSaving || !hasValidTitle
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-green-600 hover:bg-green-700'
                        }`}
                >
                    {isSaving ? 'Saving...' : 'Save Changes'}
                </button>
                <Link href="/home/<USER>">
                    <button
                        className="px-4 py-2 bg-gray-400 text-white rounded hover:bg-gray-500"
                        disabled={isSaving}
                    >
                        Cancel
                    </button>
                </Link>
            </div>
        </div>
    );
};

export default EditDiscoverPage;