"use client"

import RegionsList from '@/modules/region/components/list-region'
import React from 'react'

const TrekRegionList = () => {
  return (
    <div>
      <RegionsList/>
    </div>
  )
}

export default TrekRegionList

// "use client"

// import React, { useState } from "react";
// import { useRouter } from "next/navigation";
// import { CategoriesList, Category } from "@/components/category/category-list";

// export default function TrekkingListPage() {
//   const router = useRouter();
//   const [treks, setTreks] = useState<Category[]>([
//     { id: 1, name: "Annapurna Base Camp", slug: "abc", published: true },
//     { id: 2, name: "Everest Base Camp", slug: "ebc", published: true },
//   ]);

//   return (
//     <CategoriesList
//       title="All Treks"
//       categories={treks}
//       createUrl="/trek/create"
//       onEdit={(id) => router.push(`/trek/edit/${id}`)}
//       onDelete={(id) => setTreks((t) => t.filter((x) => x.id !== id))}
//     />
//   );
// }
