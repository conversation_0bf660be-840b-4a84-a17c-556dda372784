import { useQuery } from '@tanstack/react-query';
import { IApiResponse } from '@/types/response';
import { IPackageCostDate } from '@/types/package_';

export function useGetPackageCostDateById(id: string) {
  return useQuery<IApiResponse<IPackageCostDate[]>, Error>({
    queryKey: ['package-cost-date', id],
    queryFn: () =>
      fetch(`/api/package-cost-date/package/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then(res => res.json()),
    enabled: !!id,
  });
}
