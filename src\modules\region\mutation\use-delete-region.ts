import { IRegion } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useDeleteRegion() {
  const queryClient = useQueryClient();
  
  return useMutation<IApiResponse<IRegion>, Error, string>({
    mutationFn: (id: string) =>
      fetch(`/api/region/${id}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then(async (res) => {
        if (!res.ok) {
          const errorData = await res.json();
          throw new Error(errorData.message || 'Failed to delete region');
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['region'] });
      toast.success('Region deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(`Error deleting region: ${error.message}`);
    },
  });
}
