'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { EditTabs } from '@/modules/product/component/edit-tabs'
import { useCreatePackageCostDate } from '@/modules/package-cost-date/mutations/create-package-cost-date'
import { useDeletePackageCostDate } from '@/modules/package-cost-date/mutations/delete-package-cost-date'

import { IPackageCostDate } from '@/types/package_'
import { toast } from 'sonner'
import { useGetPackageCostDates } from '@/modules/package-cost-date/queries/get-packages-cost-date'
import { useUpdatePackageCostDate } from '@/modules/package-cost-date/mutations/update-package'
import { AddCostDateForm } from '@/modules/product/component/add-cost-date-form'
import { CostDateList } from '@/modules/product/component/list-cost-date'
import { useGetPackageCostDateById } from '@/modules/package-cost-date/queries/get-package-cost-date-by-packageId'


export default function EditCostDatePage() {
  const { id: packageId } = useParams() as { id: string }

  const { data, isLoading, isError } = useGetPackageCostDateById(packageId);

  const createMutation = useCreatePackageCostDate()
  const updateMutation = useUpdatePackageCostDate(packageId)
  const deleteMutation = useDeletePackageCostDate(packageId)

  const [items, setItems] = useState<IPackageCostDate[]>([])
  const [editingItem, setEditingItem] = useState<IPackageCostDate | null>(null)

  useEffect(() => {
    if (data?.data) {
      const costDates: IPackageCostDate[] = Array.isArray(data.data)
        ? data.data.filter(Boolean)
        : [data.data]
      setItems(costDates)
    }
  }, [data])

  const onAdd = (newItem: Omit<IPackageCostDate, 'id'>) => {
    createMutation.mutate(
      { ...newItem, packageId },
      {
        onSuccess: res => {
          if (res.data) {
            setItems(prev => [...prev, res.data!])
            toast.success('Cost & date added successfully')
          }
        },
        onError: () => toast.error('Failed to add cost & date'),
      }
    )
  }

  const onUpdate = (updatedItem: IPackageCostDate) => {
    updateMutation.mutate(
      updatedItem,
      {
        onSuccess: () => {
          setEditingItem(null)
          toast.success('Cost & date updated successfully')
        },
        onError: (error) => {
          console.error('Update failed:', error)
          toast.error('Failed to update cost & date')
        },
      }
    )
  }

  const onDelete = (id: string) => {
    deleteMutation.mutate(id, {
      onSuccess: () => toast.success('Cost & date deleted successfully'),
      onError: () => toast.error('Failed to delete cost & date'),
    })
  }

  const onEdit = (id: string) => {
    const foundItem = items.find(i => String(i.id) === String(id))
    setEditingItem(foundItem || null)
  }

  if (isLoading) return <div>Loading cost & date...</div>
  if (isError) return <div>Error loading cost & date data.</div>

  return (
    <div className="min-h-screen p-6 bg-gray-50">
      <EditTabs packageId={packageId} />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <AddCostDateForm
          editingItem={editingItem}
          onAdd={onAdd}
          onUpdate={onUpdate}
          onCancelEdit={() => setEditingItem(null)}
        />
        <CostDateList
          items={items}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      </div>
    </div>
  )
}
