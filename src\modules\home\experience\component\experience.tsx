"use client";

import React from "react";
import { useRouter } from "next/navigation"; 
import { UseGetExperience } from "../queries/use-get-experience";

const ExperienceDisplay: React.FC = () => {
  const { data, isLoading, error } = UseGetExperience();
  const router = useRouter();

  if (isLoading) return <p>Loading experience...</p>;
  if (error) return <p>Error loading experience: {error.message}</p>;

  const experience = data?.data;

  return (
    <div className="p-6 container mx-auto text-center">
      {experience ? (
        <>
          <h1 className="text-3xl font-bold mb-4">{experience.heading}</h1>
          <p className="mb-6">{experience.subHeading}</p>

          <table className="min-w-full border border-gray-300 mb-6">
            <thead>
              <tr className="bg-gray-100">
                <th className="border px-4 py-2">Feature Title</th>
                <th className="border px-4 py-2">Feature Subtitle</th>
              </tr>
            </thead>
            <tbody>
              {experience.features.map((feature, idx) => (
                <tr key={idx} className="even:bg-gray-50">
                  <td className="border px-4 py-2">{feature.title}</td>
                  <td className="border px-4 py-2">{feature.subtitle}</td>
                </tr>
              ))}
            </tbody>
          </table>

          <button
            className="bg-brand text-white px-4 py-2 rounded hover:bg-brand/80"
            onClick={() => router.push("/home/<USER>/edit")}
          >
            Edit Experience
          </button>
        </>
      ) : (
        <>
          <p className="mb-6">No experience data found.</p>
          <button
            className="bg-brand text-white px-4 py-2 rounded hover:bg-brand/80"
            onClick={() => router.push("/home/<USER>/create")}
          >
            Create Experience
          </button>
        </>
      )}
    </div>
  );
};

export default ExperienceDisplay;
