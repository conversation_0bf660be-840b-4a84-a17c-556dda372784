import { IRegion } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useCreateRegion() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IRegion>, Error>({
    mutationFn: (data) =>
      fetch(`/api/region`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['region'] });
      toast.success('Region added successfully');
    },
    onError: () => {
      toast.error('Error adding region');
    },
  });
}
