import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { IAdventure } from '@/types/home';
import { IApiResponse } from '@/types/response';

export default function UseCreateAdventure() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IAdventure>, Error, IAdventure>({
    mutationFn: (data: IAdventure) =>
      fetch(`/api/home-adventure`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adventure'] });
      toast.success('Adventure Created Sucessfully');
    },
    onError: () => {
      toast.error('Error Creating Adventure');
    },
  });
}
