import { IPackageFaq } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetPackageFaqs(params?: {
  page?: number;
  limit?: number;
  sort?: string;
  searchFields?: string;
  search?: string;
}) {
  return useQuery<IApiResponse<IPackageFaq[]>, Error>({
    queryKey: ['packages', params],
    queryFn: () =>
      fetch(
        `/api/package-faq?${new URLSearchParams(
          params as Record<string, string>
        )}`,
        {
          mode: 'cors',
          credentials: 'include',
        }
      ).then((res) => res.json()),
  });
}
