import { IPackage } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetPackageById(id: string) {
  return useQuery<IApiResponse<IPackage>, Error>({
    queryKey: ['package', id],
    queryFn: () =>
      fetch(`/api/package/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id,
  });
}
