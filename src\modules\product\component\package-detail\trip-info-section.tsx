import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { IPackageInfo, IPackageInfoItem } from "@/types/package_";
import { Trash2, Plus } from "lucide-react";
import dynamic from "next/dynamic";

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false });

interface PackageTripInfoFormProps {
  info?: Partial<IPackageInfo>;
  onInfoChange: (d: Partial<IPackageInfo>) => void;
}

export function PackageTripInfoForm({ info, onInfoChange }: PackageTripInfoFormProps) {
  const items = info?.items || [];

  const addItem = () => {
    onInfoChange({
      ...info,
      items: [
        ...items, {
          id: "",
          packageInfoId: "",
          title: "",
          details: "",
          note: "",
          createdAt: "",
          updatedAt: ""
        }
      ]
    });
  };

  const removeItem = (idx: number) => {
    onInfoChange({
      ...info,
      items: items.filter((_, i) => i !== idx)
    });
  };

  const handleItemChange = (idx: number, field: keyof IPackageInfoItem, value: string) => {
    const next = [...items];
    next[idx] = { ...next[idx], [field]: value };
    onInfoChange({ ...info, items: next });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Important Trip Info</CardTitle>
      </CardHeader>
      <CardContent>
        <Label>Title</Label>
        <Input
          value={info?.title || ""}
          onChange={e => onInfoChange({ ...info, title: e.target.value })}
          placeholder="Section Title"
        />
        <div className="flex justify-between items-center mt-2">
          <span>Items</span>
          <Button type="button" onClick={addItem} size="sm">
            <Plus className="w-4 h-4" /> Add Info
          </Button>
        </div>
        {items.map((item, idx) => (
          <div key={idx} className="border rounded p-3 my-2">
            <div className="flex justify-between">
              <Label>Item {idx + 1}</Label>
              <Button onClick={() => removeItem(idx)} size="sm" variant="outline">
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
            <Input
              placeholder="Title"
              className="my-1"
              value={item.title || ""}
              onChange={e => handleItemChange(idx, "title", e.target.value)}
            />
            <Label>Details</Label>
            <RichTextEditor
              value={item.details || ""}
              onChange={val => handleItemChange(idx, "details", val)}
            />
            <Input
              placeholder="Note"
              className="my-1"
              value={item.note || ""}
              onChange={e => handleItemChange(idx, "note", e.target.value)}
            />
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
