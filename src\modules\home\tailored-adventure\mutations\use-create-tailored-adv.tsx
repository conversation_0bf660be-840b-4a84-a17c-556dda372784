import { ITailoredAdventure } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function UseCreateTailoredAdventure() {
  const queryClient = useQueryClient();
  return useMutation<
    IApiResponse<ITailoredAdventure>,
    Error,
    ITailoredAdventure
  >({
    mutationFn: (data: ITailoredAdventure) =>
      fetch(`/api/home-tailored-adventures`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tailored-adventure'] });
      toast.success('Tailored Adventure Created Sucessfully');
    },
    onError: () => {
      toast.error('Error Creating Tailored Adventure');
    },
  });
}
