import { useQuery } from '@tanstack/react-query';
import { IApiResponse } from '@/types/response';
import { IPackageItinerary } from '@/types/package_';

export function useGetPackageItineraryById(id: string) {
  return useQuery<IApiResponse<IPackageItinerary[]>, Error>({
    queryKey: ['package-itinerary', id],
    queryFn: () =>
      fetch(`/api/package-itinerary/package/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then(res => res.json()),
    enabled: !!id,
  });
}
