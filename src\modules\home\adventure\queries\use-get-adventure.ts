import { IAdventure } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function UseGetAdventure() {
  return useQuery<IApiResponse<IAdventure[]>, Error>({
    queryKey: ['adventure'],
    queryFn: () =>
      fetch(`/api/home-adventure`, {
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((res) => res.json()),
  });
}
