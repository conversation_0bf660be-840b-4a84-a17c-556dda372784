'use client'

import { useState } from 'react'
import { useCreatePackage } from '@/modules/package/mutations/create-package'
import { useGetActivity } from '@/modules/activity/queries/use-get-activity'
import { useGetRegion } from '@/modules/region/queries/use-get-region'
import { IPackage } from '@/types/package_'
import { Button } from '@/components/ui/button'
import { PackageDetailsForm } from '../component/package-detail-form'
import { useRouter } from 'next/navigation'
import { packageSchema } from '@/modules/package/schema/package-schema'

const getInitialPackageData = (): Partial<IPackage> => ({
  name: '',
  slug: '',
  regionId: '',
  activityId: '',
  accomodation: '',
  distance: '',
  type: '',
  duration: '',
  altitude: '',
  meals: '',
  groupSize: '',
  price: '',
  discountPrice: '',
  bestSeason: '',
  transport: '',
  activityPerDay: '',
  grade: '',
  bookingLink: '',
  overviewDescription: '',
  thumbnail: '',
  mainImage: '',
  mainImageAlt: '',
  pdfBrochure: '',
  published: false,
  tripOftheMonth: false,
  popularTour: false,
  shortTrek: false,
  itinerary: [],
  equipment: [],
  costDate: [],
  groupPrice: [],
  faq: [],
  review: [],
})

export default function PackageCreatePage() {
  const router = useRouter()
  const [packageData, setPackageData] = useState<Partial<IPackage>>(getInitialPackageData())
  const { data: activityData, isLoading: activityLoading } = useGetActivity()
  const { data: regionData, isLoading: regionLoading } = useGetRegion()
  const createPackageMutation = useCreatePackage()

  const onSave = () => {
    try {
      packageSchema.parse(packageData); 

      createPackageMutation.mutate(packageData, {
        onSuccess: () => {
          alert('Package created successfully');
          setPackageData(getInitialPackageData());
          router.push('/package');
        },
        onError: (error: Error) => {
          alert(`Error creating package: ${error.message}`);
        },
      });
    } catch (err) {
      if (err instanceof ZodError) {
        err.errors.forEach((e) => toast.error(e.message));
      } else {
        toast.error('Validation failed');
      }
    }
  };

  if (activityLoading || regionLoading) return <div>Loading...</div>

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">Create New Package</h1>
      <PackageDetailsForm
        formData={packageData}
        onFormDataChange={setPackageData}
        activities={activityData?.data || []}
        regions={regionData?.data || []}
      />
      <div className="mt-6 flex justify-end">
        <Button
          onClick={onSave}
          className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          Save
        </Button>
      </div>
    </div>
  )
}
