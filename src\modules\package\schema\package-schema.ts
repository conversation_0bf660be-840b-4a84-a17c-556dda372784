import { z } from "zod";

export const packageSchema = z.object({
  name: z.string().min(1, "Package Name is required"),
  slug: z.string().min(1, "Slug is required"),
  regionId: z.string().min(1, "Region selection is required"),
  activityId: z.string().min(1, "Activity selection is required"),
  accomodation: z.string().min(1, "Accommodation is required"),
  type: z.string().min(1, "Trail Type is required"),
  altitude: z.string().min(1, "Max Altitude is required"),
  groupSize: z.string().min(1, "Group Size is required"),
  bestSeason: z.string().min(1, "Best Season is required"),
  duration: z.string().min(1, "Duration is required"),
  price: z.string().min(1, "Price is required"),
  meals: z.string().min(1, "Meals Include is required"),
  discountPrice: z.string().min(1, "Discount Price is required"),
  transport: z.string().min(1, "Transportation is required"),
  bookingLink: z.string().min(1, "Booking Link is required"),
  mainImage: z.string().min(1, "Main Image is required").url("Main Image must be a valid URL"),
  mainImageAlt: z.string().min(1, "Image Alt Tag is required"),
  thumbnail: z.string().optional(),
  pdfBrochure: z.string().optional(),
  // Add other fields validation if needed
});
