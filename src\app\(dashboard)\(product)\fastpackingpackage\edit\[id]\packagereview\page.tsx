'use client';

import { useState, useEffect, use } from 'react';
import { EditTabs } from '@/modules/product/component/edit-tabs';
import { ReviewEntry } from '@/types/package';
import { AddReviewForm } from '@/modules/product/component/add-review-form';
import { ReviewList } from '@/modules/product/component/list-review';
import { Button } from '@/components/ui/button';

interface EditReviewPageProps {
  params: Promise<{ id: string }>;
}

export default function EditReviewPage({ params }: EditReviewPageProps) {
  const { id } = use(params);
  const packageId = parseInt(id);

  const [reviewItems, setReviewItems] = useState<ReviewEntry[]>([]);
  const [editingReview, setEditingReview] = useState<ReviewEntry | null>(null);

  useEffect(() => {
    // Demo data - replace with real API fetch
    const demoReviews: ReviewEntry[] = [
      {
        id: 1,
        packageId: Number(packageId),
        name: '<PERSON>',
        email: '<EMAIL>',
        rating: '5',
        comment: 'Amazing experience!',
        publish: true,
        image: '',
      },
      {
        id: 2,
        packageId: Number(packageId),
        name: '<PERSON> Smith',
        email: '<EMAIL>',
        rating: '4',
        comment: 'Great trek but challenging.',
        publish: false,
        image: '',
      },
    ];
    setReviewItems(demoReviews);
  }, [packageId]);

  const handleAddReview = (data: Omit<ReviewEntry, 'id'>) => {
    setReviewItems((prev) => [...prev, { ...data, id: Date.now() }]);
  };

  const handleEditReview = (id: number) => {
    const found = reviewItems.find((item) => item.id === id) || null;
    setEditingReview(found);
  };

  const handleUpdateReview = (updatedItem: ReviewEntry) => {
    setReviewItems((items) =>
      items.map((item) => (item.id === updatedItem.id ? updatedItem : item))
    );
    setEditingReview(null);
  };

  const handleDeleteReview = (id: number) => {
    setReviewItems((items) => items.filter((item) => item.id !== id));
    if (editingReview?.id === id) setEditingReview(null);
  };

  const handleCancelEditReview = () => setEditingReview(null);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <EditTabs packageId={packageId} />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        {/* <div>
          <AddReviewForm
            editingItem={editingReview}
            onAdd={handleAddReview}
            onUpdate={handleUpdateReview}
            onCancelEdit={handleCancelEditReview}
          />
        </div>
        <div>
          <ReviewList
            items={reviewItems}
            onEdit={handleEditReview}
            onDelete={handleDeleteReview}
          />
        </div> */}
        <div>
          <Button className="mt-9 px-4 py-2 bg-brand text-white rounded-lg">
            Save
          </Button>
        </div>
      </div>
    </div>
  );
}
