'use client'

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { IPackageShortItinerary } from "@/types/package_"
import { Trash2, Plus } from "lucide-react"

interface PackageShortItineraryFormProps {
  shortItinerary?: Partial<IPackageShortItinerary> | undefined;
  onShortItineraryChange: (shortItinerary: Partial<IPackageShortItinerary>) => void;
}

export function PackageShortItineraryForm({ shortItinerary, onShortItineraryChange }: PackageShortItineraryFormProps) {
  const handleTitleChange = (title: string) => {
    onShortItineraryChange({
      ...shortItinerary,
      title
    });
  };

  const handlePointChange = (index: number, value: string) => {
    const points = [...(shortItinerary?.points || [])];
    points[index] = value;
    onShortItineraryChange({
      ...shortItinerary,
      points
    });
  };

  const addPoint = () => {
    const points = [...(shortItinerary?.points || []), ''];
    onShortItineraryChange({
      ...shortItinerary,
      points
    });
  };

  const removePoint = (index: number) => {
    const points = (shortItinerary?.points || []).filter((_, i) => i !== index);
    onShortItineraryChange({
      ...shortItinerary,
      points
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Short Itinerary</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="itinerary-title">Title</Label>
          <Input
            id="itinerary-title"
            value={shortItinerary?.title || ''}
            onChange={(e) => handleTitleChange(e.target.value)}
            placeholder="Enter itinerary title"
            className="border-black/20 rounded-none"
          />
        </div>
        
        <div>
          <div className="flex justify-between items-center mb-2">
            <Label>Itinerary Points</Label>
            <Button
              type="button"
              onClick={addPoint}
              size="sm"
              variant="outline"
              className="border-black/20 rounded-none"
            >
              <Plus className="w-4 h-4 mr-1" />
              Add Point
            </Button>
          </div>
          
          <div className="space-y-2">
            {(shortItinerary?.points || []).map((point, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  value={point}
                  onChange={(e) => handlePointChange(index, e.target.value)}
                  placeholder={`Day ${index + 1}: Enter itinerary point`}
                  className="border-black/20 rounded-none flex-1"
                />
                <Button
                  type="button"
                  onClick={() => removePoint(index)}
                  size="sm"
                  variant="outline"
                  className="border-red-300 text-red-600 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
