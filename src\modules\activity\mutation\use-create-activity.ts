import { IActivity } from "@/types/package_";
import { IApiResponse } from "@/types/response";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export function useCreateActivity() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IActivity>, Error, IActivity>({
    mutationFn: (data) =>
      fetch(`/api/activity`, {
        method: "POST",
        mode: "cors",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["activity"] });
      toast.success("Activity added successfully");
    },
    onError: () => {
      toast.error("Error adding activity");
    },
  });
}