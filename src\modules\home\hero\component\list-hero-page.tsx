"use client";

import React from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { UseGetHero } from "../queries/use-get-home";

const ListHeroPage: React.FC = () => {
  const { data, isLoading, error } = UseGetHero();
  const router = useRouter();

  if (isLoading) return <p>Loading hero...</p>;
  if (error) return <p>Error loading hero: {error.message}</p>;

  const hero = data?.data;

  return (
    <div className="container mx-auto p-6">
      {hero ? (
        <>
          <h1 className="text-3xl font-bold mb-4">Hero Section</h1>

          {hero.videoUrl && (
            <video controls className="w-full rounded mb-4" src={hero.videoUrl} />
          )}

          <h2 className="text-xl font-semibold">Titles</h2>
          <ul className="list-disc ml-6 mb-4">
            {hero.titles.map((title, idx) => (
              <li key={idx}>{title}</li>
            ))}
          </ul>

          <h2 className="text-xl font-semibold">Subtitles</h2>
          <ul className="list-disc ml-6 mb-4">
            {hero.subtitles.map((subtitle, idx) => (
              <li key={idx}>{subtitle}</li>
            ))}
          </ul>

          <h2 className="text-xl font-semibold">Images</h2>
          <div className="grid grid-cols-3 gap-4 mb-6">
            {hero.images.map((src, idx) => (
              <Image
                key={idx}
                src={src}
                alt={`Hero image ${idx}`}
                width={150}
                height={100}
                className="w-full h-32 object-cover rounded"
              />
            ))}
          </div>

          <button
            className="bg-brand text-white px-4 py-2 rounded hover:bg-brand/80"
            onClick={() => router.push("/home/<USER>/edit")}
          >
            Edit Hero
          </button>
        </>
      ) : (
        <>
          <h1 className="text-3xl font-bold mb-6">No Hero Section Found</h1>
          <button
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            onClick={() => router.push("/home/<USER>/create")}
          >
            Create Hero Section
          </button>
        </>
      )}
    </div>
  );
};

export default ListHeroPage;
