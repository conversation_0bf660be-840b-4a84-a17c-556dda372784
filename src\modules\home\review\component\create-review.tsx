"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { IReview, IReviewItem } from "@/types/home";
import { UseCreateReview } from "../mutations/use-create-review";
import { useGetHome } from "../../queries/get-home";
import { toast } from "sonner";
import ImageUploadSingleWithMutation from "@/modules/images/components/upload-single-image";

const CreateReview: React.FC = () => {
  const router = useRouter();
  const createReview = UseCreateReview();
  const { data: homeData } = useGetHome();

  const [reviewBlock, setReviewBlock] = useState<IReview>({
    id: "",
    homeId: homeData?.data?.id || "",
    title: "",
    subtitle: "",
    createdAt: "",
    updatedAt: "",
    reviews: [
      {
        id: "",
        homeReviewId: homeData?.data?.id || "",
        quote: "",
        image: "",
        name: "",
        designation: "",
        date: "",
        createdAt: "",
        updatedAt: "",
      },
    ],
  });

  const handleBlockFieldChange = (field: keyof IReview, value: string) => {
    setReviewBlock((prev) => ({ ...prev, [field]: value }));
  };

  const handleReviewChange = (index: number, field: keyof IReviewItem, value: string) => {
    const newReviews = [...reviewBlock.reviews];
    newReviews[index] = { ...newReviews[index], [field]: value };
    setReviewBlock((prev) => ({ ...prev, reviews: newReviews }));
  };

  const handleAddReview = () => {
    setReviewBlock((prev) => ({
      ...prev,
      reviews: [
        ...prev.reviews,
        {
          id: "",
          homeReviewId: prev.homeId,
          quote: "",
          image: "",
          name: "",
          designation: "",
          date: "",
          createdAt: "",
          updatedAt: "",
        },
      ],
    }));
  };

  const handleDeleteReview = (index: number) => {
    setReviewBlock((prev) => ({
      ...prev,
      reviews: prev.reviews.filter((_, i) => i !== index),
    }));
  };

  const handleSave = () => {
    if (!reviewBlock.homeId) {
      toast.error("Home ID is required");
      return;
    }
    createReview.mutate(reviewBlock, {
      onSuccess: () => {
        toast.success("Review created successfully");
        router.push("/home");
      },
      onError: () => {
        toast.error("Error creating review");
      },
    });
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <h1 className="text-3xl font-bold">Create Reviews</h1>

      <div>
        <label className="block mb-1 font-medium">Heading</label>
        <input
          type="text"
          value={reviewBlock.title}
          onChange={(e) => handleBlockFieldChange("title", e.target.value)}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <label className="block mb-1 font-medium">Subheading</label>
        <textarea
          value={reviewBlock.subtitle}
          onChange={(e) => handleBlockFieldChange("subtitle", e.target.value)}
          rows={3}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-4">Reviews</h2>
        {reviewBlock.reviews.map((review, index) => (
          <div key={index} className="mb-4 border rounded p-4 bg-white space-y-3">
            <textarea
              placeholder="Quote"
              value={review.quote}
              onChange={(e) => handleReviewChange(index, "quote", e.target.value)}
              rows={3}
              className="w-full rounded border px-3 py-2"
            />
            <input
              type="text"
              placeholder="Name"
              value={review.name}
              onChange={(e) => handleReviewChange(index, "name", e.target.value)}
              className="w-full rounded border px-3 py-2"
            />
            <input
              type="text"
              placeholder="Designation"
              value={review.designation}
              onChange={(e) => handleReviewChange(index, "designation", e.target.value)}
              className="w-full rounded border px-3 py-2"
            />
            <input
              type="date"
              value={review.date}
              onChange={(e) => handleReviewChange(index, "date", e.target.value)}
              className="w-full rounded border px-3 py-2"
            />
            <ImageUploadSingleWithMutation
              label="Upload Review Image"
              initialUrl={review.image}
              onUploaded={(url) => handleReviewChange(index, "image", url)}
            />
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDeleteReview(index)}
              className="mt-2"
            >
              Delete Review
            </Button>
          </div>
        ))}
        <Button onClick={handleAddReview} className="mt-3">
          + Add Review
        </Button>
      </div>

      <div className="flex justify-end space-x-3">
        <Button onClick={handleSave} className="bg-brand text-white hover:bg-brand/80">
          Create Reviews
        </Button>
      </div>
    </div>
  );
};

export default CreateReview;
