import React, { useState } from 'react';
import { useGetRegion } from '../queries/use-get-region';
import { useDeleteRegion } from '../mutation/use-delete-region'; 
import { Pen, Trash } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

const RegionsList: React.FC = () => {
  const { data, isLoading, error } = useGetRegion();
  const deleteRegion = useDeleteRegion(); 

  const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);

  if (isLoading) return <p>Loading regions...</p>;
  if (error) return <p>Error loading regions: {error.message}</p>;

  const regions = data?.data ?? [];

  const handleDelete = (id: string) => {
    setDeleteConfirmId(id);
  };

  const confirmDelete = () => {
    if (deleteConfirmId) {
      deleteRegion.mutate(deleteConfirmId, {
        onSuccess: () => {
          setDeleteConfirmId(null);
        },
      });
    }
  };

  const cancelDelete = () => {
    setDeleteConfirmId(null);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Regions List</h2>
        <Link href="/region/create">
          <Button className="px-4 py-2 rounded transition">Add Region</Button>
        </Link>
      </div>

      {deleteConfirmId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded shadow-lg max-w-sm w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Confirm Delete</h3>
            <p className="mb-6">
              Are you sure you want to delete this region? This action cannot be
              undone.
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={cancelDelete}
                disabled={deleteRegion.isPending}
                className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50 transition"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                disabled={deleteRegion.isPending}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition disabled:opacity-50"
              >
                {deleteRegion.isPending ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}

      {regions.length === 0 ? (
        <p>No regions found.</p>
      ) : (
        <table className="min-w-full border rounded">
          <thead className="bg-gray-100">
            <tr>
              <th className="p-2 border">SN</th>
              <th className="p-2 border">Name</th>
              <th className="p-2 border">Slug</th>
              <th className="p-2 border">Image</th>
              <th className="p-2 border">Description</th>
              {/* <th className="p-2 border">Created At</th>
              <th className="p-2 border">Updated At</th> */}
              <th className="p-2 border">Options</th>
            </tr>
          </thead>
          <tbody>
            {regions.map((region, index) => (
              <tr key={region.id} className="even:bg-gray-50">
                <td className="p-2 border">{index + 1}</td>
                <td className="p-2 border">{region.name}</td>
                <td className="p-2 border">{region.slug}</td>
                <td className="p-2 border">
                  {region.image ? (
                    <img
                      src={region.image}
                      alt={region.name}
                      className="w-16 h-16 object-cover rounded"
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center text-xs text-gray-500">
                      No Image
                    </div>
                  )}
                </td>
                <td className="p-2 border">
                  {region.description?.trim() ? (
                    region.description
                  ) : (
                    <em>No description</em>
                  )}
                </td>
{/* 
                <td className="p-2 border">
                  {new Date(region.createdAt).toLocaleDateString()}
                </td>
                <td className="p-2 border">
                  {new Date(region.updatedAt).toLocaleDateString()}
                </td> */}
                <td className="p-2 border">
                  <div className="flex space-x-2">
                    <Link href={`/region/edit/${region.id}`}>
                      <button className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors">
                        <Pen className="w-4 h-4" />
                      </button>
                    </Link>
                    <button
                      onClick={() => handleDelete(region.id)}
                      disabled={deleteRegion.isPending}
                      className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors disabled:opacity-50"
                      type="button"
                    >
                      <Trash className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default RegionsList;
