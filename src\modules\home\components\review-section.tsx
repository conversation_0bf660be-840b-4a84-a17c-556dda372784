"use client";

import React, { useEffect, useState } from "react";
import { UseGetReview } from "../review/queries/use-get-review";
import { UseUpdateReview } from "../review/mutations/use-update-review";
import { toast } from "sonner";
import Image from "next/image";
import Link from "next/link";
import { Pen, Trash } from "lucide-react";
import { useRouter } from "next/navigation";

const ReviewSection: React.FC = () => {
  const { data, isLoading, error } = UseGetReview();
  const updateReview = UseUpdateReview();
  const router = useRouter();

  const [reviewBlock, setReviewBlock] = useState(data?.data || null);

  useEffect(() => {
    if (data?.data) {
      setReviewBlock(data.data);
    }
  }, [data]);

  const handleDeleteReview = (reviewId: string) => {
    if (!reviewBlock) return;
    const confirmed = window.confirm("Are you sure you want to delete this review?");
    if (!confirmed) return;

    const updatedReviews = reviewBlock.reviews.filter((r) => r.id !== reviewId);
    const updatedReviewBlock = { ...reviewBlock, reviews: updatedReviews };

    updateReview.mutate(updatedReviewBlock, {
      onSuccess: () => {
        toast.success("Review deleted successfully.");
        setReviewBlock(updatedReviewBlock);
      },
      onError: (err) => {
        toast.error(`Delete failed: ${err.message}`);
      },
    });
  };

  if (isLoading) return <p>Loading reviews...</p>;
  if (error) return <p>Error loading reviews: {error.message}</p>;

  if (!reviewBlock) {
    return (
      <div className="p-6 max-w-full mx-auto">
        <p className="mb-4 text-lg font-medium">No review data found. Please create one.</p>
        <button
          className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition"
          onClick={() => router.push("/home/<USER>/create")}
        >
          Create Review
        </button>
      </div>
    );
  }

  return (
    <div>
      <section className="p-6">
        <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-3xl font-bold">Heading: {reviewBlock.title}</h1>
            <button
              className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition"
              onClick={() => router.push("/home/<USER>/edit")}
            >
              Edit Reviews
            </button>
          </div>

          {reviewBlock.subtitle && <p className="mb-6">Sub Heading: {reviewBlock.subtitle}</p>}

          <div className="bg-white rounded-lg shadow">
            {reviewBlock.reviews.length > 0 ? (
              <table className="min-w-full text-left border-separate border-spacing-0">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="border px-4 py-2">SN</th>
                    <th className="border px-4 py-2">Image</th>
                    <th className="border px-4 py-2">Quote</th>
                    <th className="border px-4 py-2">Name</th>
                    <th className="border px-4 py-2">Destination</th>
                    <th className="border px-4 py-2">Date</th>
                    <th className="border px-4 py-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {reviewBlock.reviews.map((review, idx) => (
                    <tr key={review.id} className="even:bg-gray-50">
                      <td className="border px-4 py-2">{idx + 1}</td>
                      <td className="border px-4 py-2">
                        {review.image && !review.image.includes("example.com") ? (
                          <Image
                            src={review.image}
                            alt={review.name}
                            width={64}
                            height={64}
                            className="w-16 h-16 object-cover rounded"
                          />
                        ) : (
                          <Image
                            src="/images/placeholder.jpg"
                            alt="placeholder"
                            width={64}
                            height={64}
                            className="w-16 h-16 object-cover rounded"
                          />
                        )}
                      </td>
                      <td className="border px-4 py-2">{review.quote}</td>
                      <td className="border px-4 py-2">{review.name}</td>
                      <td className="border px-4 py-2">{review.designation}</td>
                      <td className="border px-4 py-2">
                        {new Date(review.date).toLocaleDateString()}
                      </td>
                      <td className="py-2 px-4 flex gap-2">
                        <div className="flex space-x-2">
                          <Link
                            href={`/home/<USER>/edit`}
                            className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                          >
                            <Pen className="w-4 h-4" />
                          </Link>
                          <button
                            onClick={() => handleDeleteReview(review.id)}
                            className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                            type="button"
                          >
                            <Trash className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <p className="mb-6">No reviews found.</p>
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default ReviewSection;
