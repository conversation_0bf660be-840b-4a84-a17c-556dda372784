import { IPackageEquipment } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useDeletePackageEquipment(id: string) {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IPackageEquipment>, Error, any>({
    mutationFn: (equipmentId: string) =>
      fetch(`/api/package-equipment/${equipmentId}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['packages'] });
      toast.success('Package equipment deleted successfully');
    },
    onError: () => {
      toast.error('Error deleting package equipment');
    },
  });
}
