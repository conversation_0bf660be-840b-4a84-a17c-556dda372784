'use client'

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

type HighlightSectionType = 'highlights' | 'description' | 'shortItinerary' | 'photo' | 'video' | 'includes' | 'excludes' | 'map' | 'tripInfo'

interface PackageSidebarProps {
  activeHighlight: HighlightSectionType
  onHighlightChange: (highlight: HighlightSectionType) => void
}

export function PackageSidebar({ activeHighlight, onHighlightChange }: PackageSidebarProps) {
  const highlights: { id: HighlightSectionType; label: string }[] = [
    { id: 'highlights', label: 'Trip Highlights' },
    { id: 'description', label: 'Description' },
    { id: 'shortItinerary', label: 'Short Itinerary' },
    { id: 'photo', label: 'Photo' },
    { id: 'video', label: 'Video' },
    { id: 'includes', label: 'Includes' },
    { id: 'excludes', label: 'Excludes' },
    { id: 'map', label: 'Map' },
    { id: 'tripInfo', label: 'Trip Info' },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Package Details</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {highlights.map((highlight) => (
          <Button
            key={highlight.id}
            variant={activeHighlight === highlight.id ? 'secondary' : 'ghost'}
            className="w-full justify-start"
            onClick={() => onHighlightChange(highlight.id)}
          >
            {highlight.label}
          </Button>
        ))}
      </CardContent>
    </Card>
  )
}
