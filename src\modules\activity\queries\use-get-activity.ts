import { IActivity } from "@/types/package_";
import { IApiResponse } from "@/types/response";
import { useQuery } from "@tanstack/react-query";

export function useGetActivity() {
    return useQuery<IApiResponse<IActivity[]>, Error>({
        queryKey: ["activity"],
        queryFn: () =>
            fetch(`/api/activity`, {
                mode: "cors",
                credentials: "include",
            })
                .then((res) => res.json()),
    })
}